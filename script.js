// Configuration - Update these values in config.js or directly here
const SUPABASE_URL = window.CONFIG?.SUPABASE?.URL || 'YOUR_SUPABASE_URL';
const SUPABASE_ANON_KEY = window.CONFIG?.SUPABASE?.ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';
const EMAILJS_SERVICE_ID = window.CONFIG?.EMAILJS?.SERVICE_ID || 'YOUR_EMAILJS_SERVICE_ID';
const EMAILJS_TEMPLATE_ID = window.CONFIG?.EMAILJS?.TEMPLATE_ID || 'YOUR_EMAILJS_TEMPLATE_ID';
const EMAILJS_USER_ID = window.CONFIG?.EMAILJS?.USER_ID || 'YOUR_EMAILJS_USER_ID';

// Check if configuration is set up
function checkConfiguration() {
    const isSupabaseConfigured = SUPABASE_URL !== 'YOUR_SUPABASE_URL' && SUPABASE_ANON_KEY !== 'YOUR_SUPABASE_ANON_KEY';
    const isEmailJSConfigured = EMAILJS_SERVICE_ID !== 'YOUR_EMAILJS_SERVICE_ID' &&
                               EMAILJS_TEMPLATE_ID !== 'YOUR_EMAILJS_TEMPLATE_ID' &&
                               EMAILJS_USER_ID !== 'YOUR_EMAILJS_USER_ID';

    if (!isSupabaseConfigured) {
        console.warn('Supabase configuration not set up. Please update the configuration in script.js or config.js');
        showConfigurationError('Supabase');
        return false;
    }

    if (!isEmailJSConfigured) {
        console.warn('EmailJS configuration not set up. Email notifications will not work.');
    }

    return true;
}

// Show configuration error
function showConfigurationError(service) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    errorDiv.innerHTML = `
        <strong>Configuration Error:</strong> ${service} is not configured.<br>
        Please check the README.md for setup instructions.
    `;
    document.body.appendChild(errorDiv);

    setTimeout(() => {
        document.body.removeChild(errorDiv);
    }, 10000);
}

// Initialize Supabase client
let supabase;
try {
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
} catch (error) {
    console.error('Failed to initialize Supabase:', error);
}

// Initialize EmailJS
try {
    emailjs.init(EMAILJS_USER_ID);
} catch (error) {
    console.error('Failed to initialize EmailJS:', error);
}

// Global variables
let currentUser = null;
let portfolioData = null;

// DOM Elements
const authSection = document.getElementById('auth-section');
const portfolioSection = document.getElementById('portfolio-section');
const previewSection = document.getElementById('preview-section');
const loginTab = document.getElementById('login-tab');
const signupTab = document.getElementById('signup-tab');
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');
const portfolioForm = document.getElementById('portfolio-form');
const userEmailSpan = document.getElementById('user-email');
const logoutBtn = document.getElementById('logout-btn');
const addProjectBtn = document.getElementById('add-project');
const projectsContainer = document.getElementById('projects-container');
const loading = document.getElementById('loading');
const successMessage = document.getElementById('success-message');
const editPortfolioBtn = document.getElementById('edit-portfolio');
const sharePortfolioBtn = document.getElementById('share-portfolio');

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    // Check configuration first
    if (!checkConfiguration()) {
        return;
    }

    setupEventListeners();
    await checkAuthState();

    // Check for public portfolio on page load
    await loadPublicPortfolio();
});

// Setup event listeners
function setupEventListeners() {
    // Auth tab switching
    loginTab.addEventListener('click', () => switchAuthTab('login'));
    signupTab.addEventListener('click', () => switchAuthTab('signup'));
    
    // Auth forms
    loginForm.addEventListener('submit', handleLogin);
    signupForm.addEventListener('submit', handleSignup);
    
    // Portfolio form
    portfolioForm.addEventListener('submit', handlePortfolioSubmit);
    
    // Logout
    logoutBtn.addEventListener('click', handleLogout);
    
    // Add project
    addProjectBtn.addEventListener('click', addProjectField);
    
    // Remove project (event delegation)
    projectsContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-project')) {
            removeProjectField(e.target.closest('.project-item'));
        }
    });
    
    // Preview actions
    editPortfolioBtn.addEventListener('click', () => {
        previewSection.classList.add('hidden');
        portfolioSection.classList.remove('hidden');
    });
    
    sharePortfolioBtn.addEventListener('click', sharePortfolio);
}

// Switch between login and signup tabs
function switchAuthTab(tab) {
    if (tab === 'login') {
        loginTab.classList.add('active');
        signupTab.classList.remove('active');
        loginForm.classList.add('active');
        signupForm.classList.remove('active');
    } else {
        signupTab.classList.add('active');
        loginTab.classList.remove('active');
        signupForm.classList.add('active');
        loginForm.classList.remove('active');
    }
}

// Check authentication state
async function checkAuthState() {
    try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
            currentUser = session.user;
            showPortfolioSection();
        } else {
            showAuthSection();
        }
    } catch (error) {
        console.error('Error checking auth state:', error);
        showAuthSection();
    }
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    const errorDiv = document.getElementById('login-error');
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });
        
        if (error) throw error;
        
        currentUser = data.user;
        showPortfolioSection();
        clearForm(loginForm);
        
    } catch (error) {
        errorDiv.textContent = error.message;
    } finally {
        showLoading(false);
    }
}

// Handle signup
async function handleSignup(e) {
    e.preventDefault();
    
    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm').value;
    const errorDiv = document.getElementById('signup-error');
    
    // Validate password confirmation
    if (password !== confirmPassword) {
        errorDiv.textContent = 'Passwords do not match';
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signUp({
            email,
            password
        });
        
        if (error) throw error;
        
        if (data.user) {
            currentUser = data.user;
            showPortfolioSection();
            clearForm(signupForm);
        }
        
    } catch (error) {
        errorDiv.textContent = error.message;
    } finally {
        showLoading(false);
    }
}

// Handle logout
async function handleLogout() {
    try {
        await supabase.auth.signOut();
        currentUser = null;
        portfolioData = null;
        showAuthSection();
        clearAllForms();
    } catch (error) {
        console.error('Error logging out:', error);
    }
}

// Show authentication section
function showAuthSection() {
    authSection.classList.remove('hidden');
    portfolioSection.classList.add('hidden');
    previewSection.classList.add('hidden');
}

// Show portfolio section
function showPortfolioSection() {
    authSection.classList.add('hidden');
    portfolioSection.classList.remove('hidden');
    previewSection.classList.add('hidden');
    userEmailSpan.textContent = currentUser.email;
}

// Show preview section
function showPreviewSection() {
    authSection.classList.add('hidden');
    portfolioSection.classList.add('hidden');
    previewSection.classList.remove('hidden');
}

// Add project field
function addProjectField() {
    const projectItem = document.createElement('div');
    projectItem.className = 'project-item';
    projectItem.innerHTML = `
        <div class="form-group">
            <label>Project Title</label>
            <input type="text" class="project-title" required>
        </div>
        <div class="form-group">
            <label>Project Description</label>
            <textarea class="project-description" rows="3" required></textarea>
        </div>
        <button type="button" class="btn btn-danger remove-project">Remove</button>
    `;
    projectsContainer.appendChild(projectItem);
}

// Remove project field
function removeProjectField(projectItem) {
    if (projectsContainer.children.length > 1) {
        projectItem.remove();
    }
}

// Handle portfolio form submission
async function handlePortfolioSubmit(e) {
    e.preventDefault();

    if (!currentUser) {
        alert('Please log in first');
        return;
    }

    // Clear any previous error messages
    const existingErrors = document.querySelectorAll('.form-error');
    existingErrors.forEach(error => error.remove());

    showLoading(true);

    try {
        // Collect and validate form data
        portfolioData = collectPortfolioData();

        // Check username uniqueness if provided
        if (portfolioData.username) {
            await checkUsernameAvailability(portfolioData.username);
        }

        // Save to Supabase
        await savePortfolioToDatabase(portfolioData);

        // Send confirmation email (don't fail if this fails)
        try {
            await sendConfirmationEmail(portfolioData);
        } catch (emailError) {
            console.warn('Failed to send confirmation email:', emailError);
        }

        // Show preview
        renderPortfolioPreview(portfolioData);
        showPreviewSection();
        showSuccessMessage();

    } catch (error) {
        console.error('Error creating portfolio:', error);
        showFormError(error.message);
    } finally {
        showLoading(false);
    }
}

// Check if username is available
async function checkUsernameAvailability(username) {
    if (!supabase) return;

    const { data, error } = await supabase
        .from('portfolios')
        .select('username')
        .eq('username', username)
        .neq('user_id', currentUser.id)
        .single();

    if (error && error.code !== 'PGRST116') {
        throw error;
    }

    if (data) {
        throw new Error('Username is already taken. Please choose a different one.');
    }
}

// Show form error message
function showFormError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error';
    errorDiv.style.cssText = `
        background: #f8d7da;
        color: #721c24;
        padding: 12px 16px;
        border-radius: 8px;
        margin: 20px 0;
        border: 1px solid #f5c6cb;
        text-align: center;
    `;
    errorDiv.textContent = message;

    const submitButton = portfolioForm.querySelector('button[type="submit"]');
    portfolioForm.insertBefore(errorDiv, submitButton);

    // Scroll to error
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Collect portfolio data from form
function collectPortfolioData() {
    const projects = [];
    const projectItems = projectsContainer.querySelectorAll('.project-item');

    projectItems.forEach(item => {
        const title = item.querySelector('.project-title').value.trim();
        const description = item.querySelector('.project-description').value.trim();

        if (title && description) {
            projects.push({ title, description });
        }
    });

    const skills = document.getElementById('portfolio-skills').value
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill);

    // Validate required fields
    const name = document.getElementById('portfolio-name').value.trim();
    const bio = document.getElementById('portfolio-bio').value.trim();

    if (!name) {
        throw new Error('Name is required');
    }

    if (!bio) {
        throw new Error('Bio is required');
    }

    if (skills.length === 0) {
        throw new Error('At least one skill is required');
    }

    if (projects.length === 0) {
        throw new Error('At least one project is required');
    }

    // Validate username if provided
    const username = document.getElementById('portfolio-username').value.trim();
    if (username && !/^[a-zA-Z0-9_-]+$/.test(username)) {
        throw new Error('Username can only contain letters, numbers, hyphens, and underscores');
    }

    // Validate URLs if provided
    const githubUrl = document.getElementById('portfolio-github').value.trim();
    const linkedinUrl = document.getElementById('portfolio-linkedin').value.trim();

    if (githubUrl && !isValidUrl(githubUrl)) {
        throw new Error('Please enter a valid GitHub URL');
    }

    if (linkedinUrl && !isValidUrl(linkedinUrl)) {
        throw new Error('Please enter a valid LinkedIn URL');
    }

    return {
        user_id: currentUser.id,
        name: name,
        username: username || null,
        bio: bio,
        skills: skills,
        github_url: githubUrl || null,
        linkedin_url: linkedinUrl || null,
        projects: projects,
        created_at: new Date().toISOString()
    };
}

// Validate URL format
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Utility functions
function showLoading(show) {
    loading.classList.toggle('hidden', !show);
}

function showSuccessMessage() {
    successMessage.classList.remove('hidden');
    setTimeout(() => {
        successMessage.classList.add('hidden');
    }, 5000);
}

function clearForm(form) {
    form.reset();
    const errorDiv = form.querySelector('.error-message');
    if (errorDiv) errorDiv.textContent = '';
}

function clearAllForms() {
    clearForm(loginForm);
    clearForm(signupForm);
    clearForm(portfolioForm);
}

// Save portfolio to Supabase database
async function savePortfolioToDatabase(data) {
    try {
        // First, check if user already has a portfolio
        const { data: existingPortfolio, error: fetchError } = await supabase
            .from('portfolios')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();

        if (fetchError && fetchError.code !== 'PGRST116') {
            throw fetchError;
        }

        if (existingPortfolio) {
            // Update existing portfolio
            const { error } = await supabase
                .from('portfolios')
                .update(data)
                .eq('user_id', currentUser.id);

            if (error) throw error;
        } else {
            // Insert new portfolio
            const { error } = await supabase
                .from('portfolios')
                .insert([data]);

            if (error) throw error;
        }

        console.log('Portfolio saved successfully');
    } catch (error) {
        console.error('Error saving portfolio:', error);
        throw error;
    }
}

// Send confirmation email using EmailJS
async function sendConfirmationEmail(data) {
    try {
        const templateParams = {
            to_email: currentUser.email,
            user_name: data.name,
            portfolio_url: data.username ?
                `${window.location.origin}?portfolio=${data.username}` :
                `${window.location.origin}?user=${currentUser.id}`,
            skills_count: data.skills.length,
            projects_count: data.projects.length
        };

        await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams);
        console.log('Confirmation email sent successfully');
    } catch (error) {
        console.error('Error sending email:', error);
        // Don't throw error here as email is optional
    }
}

// Render portfolio preview
function renderPortfolioPreview(data) {
    const previewContainer = document.getElementById('portfolio-preview');

    const skillsBadges = data.skills.map(skill =>
        `<span class="skill-badge">${skill}</span>`
    ).join('');

    const socialLinks = [];
    if (data.github_url) {
        socialLinks.push(`<a href="${data.github_url}" target="_blank" class="social-link">GitHub</a>`);
    }
    if (data.linkedin_url) {
        socialLinks.push(`<a href="${data.linkedin_url}" target="_blank" class="social-link">LinkedIn</a>`);
    }

    const projectCards = data.projects.map(project => `
        <div class="project-card">
            <h4 class="project-title">${project.title}</h4>
            <p class="project-description">${project.description}</p>
        </div>
    `).join('');

    previewContainer.innerHTML = `
        <div class="preview-header">
            <h1 class="preview-name">${data.name}</h1>
            <p class="preview-bio">${data.bio}</p>
        </div>

        <div class="skills-container">
            ${skillsBadges}
        </div>

        ${socialLinks.length > 0 ? `
            <div class="social-links">
                ${socialLinks.join('')}
            </div>
        ` : ''}

        ${data.projects.length > 0 ? `
            <div class="projects-section">
                <h3 style="text-align: center; margin-bottom: 20px; color: #333;">Projects</h3>
                <div class="projects-grid">
                    ${projectCards}
                </div>
            </div>
        ` : ''}
    `;
}

// Share portfolio function
function sharePortfolio() {
    if (!portfolioData) return;

    const portfolioUrl = portfolioData.username ?
        `${window.location.origin}?portfolio=${portfolioData.username}` :
        `${window.location.origin}?user=${currentUser.id}`;

    if (navigator.share) {
        navigator.share({
            title: `${portfolioData.name}'s Portfolio`,
            text: `Check out ${portfolioData.name}'s professional portfolio`,
            url: portfolioUrl
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(portfolioUrl).then(() => {
            alert('Portfolio URL copied to clipboard!');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = portfolioUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Portfolio URL copied to clipboard!');
        });
    }
}

// Load portfolio by username or user ID (for public viewing)
async function loadPublicPortfolio() {
    const urlParams = new URLSearchParams(window.location.search);
    const username = urlParams.get('portfolio');
    const userId = urlParams.get('user');

    if (!username && !userId) return;

    try {
        showLoading(true);

        let query = supabase.from('portfolios').select('*');

        if (username) {
            query = query.eq('username', username);
        } else {
            query = query.eq('user_id', userId);
        }

        const { data, error } = await query.single();

        if (error) throw error;

        if (data) {
            portfolioData = data;
            renderPortfolioPreview(data);

            // Hide auth and form sections, show only preview
            authSection.classList.add('hidden');
            portfolioSection.classList.add('hidden');
            previewSection.classList.remove('hidden');

            // Hide edit button for public view
            editPortfolioBtn.style.display = 'none';
        }

    } catch (error) {
        console.error('Error loading public portfolio:', error);
        alert('Portfolio not found');
    } finally {
        showLoading(false);
    }
}

// Listen for auth state changes
if (supabase) {
    supabase.auth.onAuthStateChange((event, session) => {
        if (event === 'SIGNED_IN') {
            currentUser = session.user;
            showPortfolioSection();
        } else if (event === 'SIGNED_OUT') {
            currentUser = null;
            portfolioData = null;
            showAuthSection();
        }
    });
}
