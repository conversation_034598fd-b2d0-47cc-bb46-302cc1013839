// Configuration - Update these values in config.js or directly here
const SUPABASE_URL = window.CONFIG?.SUPABASE?.URL || 'YOUR_SUPABASE_URL';
const SUPABASE_ANON_KEY = window.CONFIG?.SUPABASE?.ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';
const EMAILJS_SERVICE_ID = window.CONFIG?.EMAILJS?.SERVICE_ID || 'YOUR_EMAILJS_SERVICE_ID';
const EMAILJS_TEMPLATE_ID = window.CONFIG?.EMAILJS?.TEMPLATE_ID || 'YOUR_EMAILJS_TEMPLATE_ID';
const EMAILJS_USER_ID = window.CONFIG?.EMAILJS?.USER_ID || 'YOUR_EMAILJS_USER_ID';

// Check if configuration is set up
function checkConfiguration() {
    const isSupabaseConfigured = SUPABASE_URL !== 'YOUR_SUPABASE_URL' && SUPABASE_ANON_KEY !== 'YOUR_SUPABASE_ANON_KEY';
    const isEmailJSConfigured = EMAILJS_SERVICE_ID !== 'YOUR_EMAILJS_SERVICE_ID' &&
                               EMAILJS_TEMPLATE_ID !== 'YOUR_EMAILJS_TEMPLATE_ID' &&
                               EMAILJS_USER_ID !== 'YOUR_EMAILJS_USER_ID';

    if (!isSupabaseConfigured) {
        console.warn('Supabase configuration not set up. Please update the configuration in script.js or config.js');
        showConfigurationError('Supabase');
        return false;
    }

    if (!isEmailJSConfigured) {
        console.warn('EmailJS configuration not set up. Email notifications will not work.');
    }

    return true;
}

// Show configuration error
function showConfigurationError(service) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    errorDiv.innerHTML = `
        <strong>Configuration Error:</strong> ${service} is not configured.<br>
        Please check the README.md for setup instructions.
    `;
    document.body.appendChild(errorDiv);

    setTimeout(() => {
        document.body.removeChild(errorDiv);
    }, 10000);
}

// Initialize Supabase client
let supabase;
try {
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
} catch (error) {
    console.error('Failed to initialize Supabase:', error);
}

// Initialize EmailJS v4
try {
    if (typeof emailjs !== 'undefined') {
        emailjs.init({
            publicKey: EMAILJS_USER_ID
        });
        console.log('EmailJS v4 initialized successfully');
    }
} catch (error) {
    console.error('Failed to initialize EmailJS:', error);
}

// Global variables
let currentUser = null;
let portfolioData = null;

// DOM Elements
const heroSection = document.getElementById('hero-section');
const authModal = document.getElementById('auth-modal');
const portfolioSection = document.getElementById('portfolio-section');
const previewSection = document.getElementById('preview-section');
const headerActions = document.querySelector('.header-actions');
const userMenu = document.querySelector('.user-menu');
const userEmail = document.getElementById('user-email');
const loginBtn = document.getElementById('login-btn');
const signupBtn = document.getElementById('signup-btn');
const logoutBtn = document.getElementById('logout-btn');
const getStartedMainBtn = document.getElementById('get-started-main');
const viewDemoBtn = document.getElementById('view-demo');
const authClose = document.getElementById('auth-close');
const authOverlay = document.getElementById('auth-overlay');
const loginTab = document.getElementById('login-tab');
const signupTab = document.getElementById('signup-tab');
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');
const portfolioForm = document.getElementById('portfolio-form');
const addProjectBtn = document.getElementById('add-project');
const projectsContainer = document.getElementById('projects-container');
const saveDraftBtn = document.getElementById('save-draft');
const loading = document.getElementById('loading');
const successMessage = document.getElementById('success-message');
const editPortfolioBtn = document.getElementById('edit-portfolio');
const sharePortfolioBtn = document.getElementById('share-portfolio');
const shareModal = document.getElementById('share-modal');

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing app...');

    // Check configuration first
    if (!checkConfiguration()) {
        return;
    }

    // Setup event listeners after DOM is ready
    setupEventListeners();

    // Check authentication state
    await checkAuthState();

    // Check for public portfolio on page load
    await loadPublicPortfolio();

    console.log('App initialized successfully');
});

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Get DOM elements fresh
    const loginBtn = document.getElementById('login-btn');
    const signupBtn = document.getElementById('signup-btn');
    const logoutBtn = document.getElementById('logout-btn');
    const getStartedMainBtn = document.getElementById('get-started-main');
    const viewDemoBtn = document.getElementById('view-demo');
    const authClose = document.getElementById('auth-close');
    const authOverlay = document.getElementById('auth-overlay');
    const loginTab = document.getElementById('login-tab');
    const signupTab = document.getElementById('signup-tab');
    const loginForm = document.getElementById('login-form');
    const signupForm = document.getElementById('signup-form');
    const portfolioForm = document.getElementById('portfolio-form');
    const saveDraftBtn = document.getElementById('save-draft');
    const addProjectBtn = document.getElementById('add-project');
    const projectsContainer = document.getElementById('projects-container');

    // Header buttons
    if (loginBtn) {
        console.log('Login button found, adding listener');
        loginBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Login button clicked');
            showAuthModal('login');
        });
    } else {
        console.log('Login button not found');
    }

    if (signupBtn) {
        console.log('Signup button found, adding listener');
        signupBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Signup button clicked');
            showAuthModal('signup');
        });
    } else {
        console.log('Signup button not found');
    }

    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            handleLogout();
        });
    }

    // Hero section
    if (getStartedMainBtn) {
        getStartedMainBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Get started button clicked');
            showAuthModal('signup');
        });
    }

    if (viewDemoBtn) {
        viewDemoBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showDemo();
        });
    }

    // Auth modal
    if (authClose) {
        authClose.addEventListener('click', (e) => {
            e.preventDefault();
            hideAuthModal();
        });
    }

    if (authOverlay) {
        authOverlay.addEventListener('click', (e) => {
            if (e.target === authOverlay) {
                hideAuthModal();
            }
        });
    }

    // Auth tab switching
    if (loginTab) {
        loginTab.addEventListener('click', (e) => {
            e.preventDefault();
            switchAuthTab('login');
        });
    }

    if (signupTab) {
        signupTab.addEventListener('click', (e) => {
            e.preventDefault();
            switchAuthTab('signup');
        });
    }

    // Auth forms - Add event listeners with more debugging
    if (loginForm) {
        console.log('✅ Login form found, adding submit listener');
        loginForm.addEventListener('submit', (e) => {
            console.log('🔥 LOGIN FORM SUBMIT EVENT TRIGGERED');
            handleLogin(e);
        });

        // Also add click listener to submit button
        const loginSubmitBtn = loginForm.querySelector('button[type="submit"]');
        if (loginSubmitBtn) {
            console.log('✅ Login submit button found');
            loginSubmitBtn.addEventListener('click', (e) => {
                console.log('🔥 LOGIN SUBMIT BUTTON CLICKED');
                // Let the form handle the submission
            });
        }
    } else {
        console.log('❌ Login form not found');
    }

    if (signupForm) {
        console.log('✅ Signup form found, adding submit listener');
        signupForm.addEventListener('submit', (e) => {
            console.log('🔥 SIGNUP FORM SUBMIT EVENT TRIGGERED');
            handleSignup(e);
        });

        // Also add click listener to submit button
        const signupSubmitBtn = signupForm.querySelector('button[type="submit"]');
        if (signupSubmitBtn) {
            console.log('✅ Signup submit button found');
            signupSubmitBtn.addEventListener('click', (e) => {
                console.log('🔥 SIGNUP SUBMIT BUTTON CLICKED');
                // Let the form handle the submission
            });
        }
    } else {
        console.log('❌ Signup form not found');
    }

    // Portfolio form
    if (portfolioForm) {
        portfolioForm.addEventListener('submit', handlePortfolioSubmit);
    }

    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', (e) => {
            e.preventDefault();
            handleSaveDraft();
        });
    }

    // Add project
    if (addProjectBtn) {
        addProjectBtn.addEventListener('click', (e) => {
            e.preventDefault();
            addProjectField();
        });
    }

    // Remove project (event delegation)
    if (projectsContainer) {
        projectsContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-remove') || e.target.closest('.btn-remove')) {
                e.preventDefault();
                removeProjectField(e.target.closest('.project-item'));
            }
        });
    }

    // Skills preview
    const skillsInput = document.getElementById('portfolio-skills');
    if (skillsInput) {
        skillsInput.addEventListener('input', updateSkillsPreview);
    }

    // Bio character counter
    const bioInput = document.getElementById('portfolio-bio');
    if (bioInput) {
        bioInput.addEventListener('input', updateCharCounter);
    }

    console.log('Event listeners setup complete');

    // Test button functionality
    setTimeout(() => {
        const testBtn = document.getElementById('login-btn');
        if (testBtn) {
            console.log('Login button found after setup:', testBtn);
        } else {
            console.log('Login button still not found after setup');
        }
    }, 1000);
}

// Show/hide auth modal
function showAuthModal(defaultTab = 'login') {
    console.log('Showing auth modal with tab:', defaultTab);
    const modal = document.getElementById('auth-modal');
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        switchAuthTab(defaultTab);
    } else {
        console.error('Auth modal not found');
    }
}

// Show demo
function showDemo() {
    console.log('Demo button clicked');
    showNotification('Demo feature coming soon! 🚀', 'info');
}

// Test Supabase connection
async function testSupabase() {
    console.log('🔍 Testing Supabase connection...');
    console.log('Supabase URL:', SUPABASE_URL);
    console.log('Supabase client:', supabase);

    if (!supabase) {
        console.log('❌ Supabase client is not initialized');
        showNotification('Supabase connection failed! ❌', 'error');
        return;
    }

    try {
        // First test basic connection
        console.log('🔗 Testing basic Supabase connection...');

        // Test auth connection
        const { data: authData, error: authError } = await supabase.auth.getSession();
        if (authError) {
            console.log('❌ Auth connection error:', authError);
            showNotification(`Auth connection failed: ${authError.message}`, 'error');
            return;
        } else {
            console.log('✅ Auth connection working');
        }

        // Test database connection by trying to read from portfolios table
        console.log('🗄️ Testing database connection...');
        const { data, error } = await supabase
            .from('portfolios')
            .select('id')
            .limit(1);

        if (error) {
            console.log('❌ Database error:', error);
            if (error.message.includes('relation "portfolios" does not exist')) {
                showNotification('Database table missing! Need to run SQL setup.', 'error');
                console.log('💡 Run the SQL commands from supabase-setup.sql in your Supabase SQL editor');
            } else {
                showNotification(`Database error: ${error.message}`, 'error');
            }
        } else {
            console.log('✅ Database connection working, data:', data);
            showNotification('Supabase is fully connected! ✅', 'success');
        }
    } catch (error) {
        console.log('❌ Connection test failed:', error);
        showNotification(`Connection test failed: ${error.message}`, 'error');
    }
}

// Test authentication with dummy data
async function testAuth() {
    console.log('🔐 Testing authentication...');

    if (!supabase) {
        showNotification('Supabase not initialized', 'error');
        return;
    }

    try {
        const testEmail = `test${Date.now()}@example.com`; // Unique email
        const testPassword = 'test123456';

        showLoading(true, 'Testing authentication...');
        console.log('📧 Testing with email:', testEmail);

        // First try to sign up
        console.log('🚀 Attempting signup...');
        const { data: signupData, error: signupError } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword
        });

        if (signupError) {
            console.log('❌ Signup error:', signupError);

            // If user already exists, try to sign in instead
            if (signupError.message.includes('already registered')) {
                console.log('🔄 User exists, trying login...');
                const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>', // Use fixed email for login test
                    password: testPassword
                });

                if (loginError) {
                    console.log('❌ Login error:', loginError);
                    showNotification(`Login test failed: ${loginError.message}`, 'error');
                } else {
                    console.log('✅ Login test successful:', loginData);
                    showNotification('Authentication (Login) is working! ✅', 'success');
                    await supabase.auth.signOut();
                }
            } else {
                showNotification(`Signup test failed: ${signupError.message}`, 'error');
            }
        } else {
            console.log('✅ Signup test successful:', signupData);
            showNotification('Authentication (Signup) is working! ✅', 'success');

            // Clean up - sign out the test user
            if (signupData.user) {
                await supabase.auth.signOut();
            }
        }
    } catch (error) {
        console.log('❌ Auth test exception:', error);
        showNotification(`Auth test failed: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Test login with pre-filled data
function testLogin() {
    console.log('Testing login with demo credentials...');

    // Fill the form
    document.getElementById('login-email').value = '<EMAIL>';
    document.getElementById('login-password').value = 'test123456';

    // Trigger the login
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        console.log('📝 Dispatching submit event to login form');
        const event = new Event('submit', { bubbles: true, cancelable: true });
        loginForm.dispatchEvent(event);
    } else {
        console.log('❌ Login form not found for test');
    }
}

// Direct login test bypassing form
async function directLoginTest() {
    console.log('🧪 Testing direct login...');

    if (!supabase) {
        console.log('❌ Supabase not available');
        showNotification('Supabase not initialized', 'error');
        return;
    }

    showLoading(true, 'Testing direct login...');

    try {
        console.log('🔑 Attempting direct login with test credentials...');
        const { data, error } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'test123456'
        });

        if (error) {
            console.log('❌ Direct login error:', error);

            // If user doesn't exist, create it first
            if (error.message.includes('Invalid login credentials')) {
                console.log('🔄 User not found, creating account first...');
                const { data: signupData, error: signupError } = await supabase.auth.signUp({
                    email: '<EMAIL>',
                    password: 'test123456'
                });

                if (signupError) {
                    console.log('❌ Signup error:', signupError);
                    showNotification(`Account creation failed: ${signupError.message}`, 'error');
                } else {
                    console.log('✅ Account created, now try logging in manually');
                    showNotification('Test account created! Now try the login form.', 'success');
                }
            } else {
                showNotification(`Direct login failed: ${error.message}`, 'error');
            }
        } else {
            console.log('✅ Direct login successful:', data);
            currentUser = data.user;
            hideAuthModal();
            showPortfolioSection();
            showNotification('Direct login successful! 🎉', 'success');
        }
    } catch (error) {
        console.log('❌ Direct login exception:', error);
        showNotification(`Direct login exception: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Debug form elements
function debugForms() {
    console.log('🔍 DEBUGGING FORM ELEMENTS:');

    const loginForm = document.getElementById('login-form');
    const signupForm = document.getElementById('signup-form');
    const loginEmail = document.getElementById('login-email');
    const loginPassword = document.getElementById('login-password');
    const signupEmail = document.getElementById('signup-email');

    console.log('Login form:', loginForm);
    console.log('Signup form:', signupForm);
    console.log('Login email input:', loginEmail);
    console.log('Login password input:', loginPassword);
    console.log('Signup email input:', signupEmail);

    if (loginForm) {
        console.log('✅ Login form found');
        console.log('Form action:', loginForm.action);
        console.log('Form method:', loginForm.method);
        console.log('Form onsubmit:', loginForm.onsubmit);

        const submitBtn = loginForm.querySelector('button[type="submit"]');
        console.log('Submit button:', submitBtn);
    } else {
        console.log('❌ Login form NOT found');
    }

    // Test if we can access the auth modal
    const authModal = document.getElementById('auth-modal');
    console.log('Auth modal:', authModal);
    console.log('Auth modal classes:', authModal?.className);
}

// Manual login trigger
function manualLogin() {
    console.log('🔧 Manual login trigger');

    // Fill the form first
    const emailInput = document.getElementById('login-email');
    const passwordInput = document.getElementById('login-password');

    if (emailInput && passwordInput) {
        emailInput.value = '<EMAIL>';
        passwordInput.value = 'test123456';
        console.log('✅ Form filled with test data');

        // Create a fake event and call handleLogin directly
        const fakeEvent = {
            preventDefault: () => console.log('preventDefault called')
        };

        console.log('🚀 Calling handleLogin directly...');
        handleLogin(fakeEvent);
    } else {
        console.log('❌ Could not find email/password inputs');
    }
}

// Quick signup test
async function quickSignup() {
    console.log('🚀 Quick signup test');

    if (!supabase) {
        showNotification('Supabase not initialized', 'error');
        return;
    }

    const testEmail = '<EMAIL>';
    const testPassword = 'test123456';

    showLoading(true, 'Creating test account...');

    try {
        const { data, error } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword
        });

        if (error) {
            console.log('❌ Signup error:', error);
            showNotification(`Signup failed: ${error.message}`, 'error');
        } else {
            console.log('✅ Signup successful:', data);
            showNotification('Test account created! Now try logging in.', 'success');
        }
    } catch (error) {
        console.log('❌ Signup exception:', error);
        showNotification(`Signup exception: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
}

// Auto-setup database table
async function setupDatabase() {
    console.log('🛠️ Setting up database...');

    if (!supabase) {
        showNotification('Supabase not initialized', 'error');
        return;
    }

    showLoading(true, 'Setting up database...');

    try {
        // Try to create the portfolios table
        const { data, error } = await supabase.rpc('create_portfolios_table');

        if (error) {
            console.log('❌ Database setup error:', error);
            showNotification(`Database setup failed: ${error.message}`, 'error');

            // Show manual setup instructions
            console.log(`
🔧 MANUAL SETUP REQUIRED:
1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/lwmqufwuzmmruecyaxgg
2. Click on "SQL Editor"
3. Run the SQL commands from supabase-setup.sql file
4. Then try the tests again
            `);
            showNotification('Manual database setup required. Check console for instructions.', 'error');
        } else {
            console.log('✅ Database setup successful');
            showNotification('Database setup complete! ✅', 'success');
        }
    } catch (error) {
        console.log('❌ Database setup exception:', error);
        showNotification('Manual database setup required. Check supabase-setup.sql file.', 'error');
    } finally {
        showLoading(false);
    }
}

// Complete setup test
async function completeSetupTest() {
    console.log('🔄 Running complete setup test...');

    // Test 1: Supabase connection
    console.log('Step 1: Testing Supabase connection...');
    await testSupabase();

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Authentication
    console.log('Step 2: Testing authentication...');
    await testAuth();

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 3: Direct login
    console.log('Step 3: Testing direct login...');
    await directLoginTest();
}

// Make functions globally available
window.showAuthModal = showAuthModal;
window.showDemo = showDemo;
window.testSupabase = testSupabase;
window.testAuth = testAuth;
window.testLogin = testLogin;
window.directLoginTest = directLoginTest;
window.debugForms = debugForms;
window.manualLogin = manualLogin;
window.quickSignup = quickSignup;
window.setupDatabase = setupDatabase;
window.completeSetupTest = completeSetupTest;

function hideAuthModal() {
    console.log('Hiding auth modal');
    const modal = document.getElementById('auth-modal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
}

// Switch between login and signup tabs
function switchAuthTab(tab) {
    console.log('Switching to tab:', tab);

    const loginTab = document.getElementById('login-tab');
    const signupTab = document.getElementById('signup-tab');
    const loginContainer = document.getElementById('login-form-container');
    const signupContainer = document.getElementById('signup-form-container');

    if (tab === 'login') {
        if (loginTab) loginTab.classList.add('active');
        if (signupTab) signupTab.classList.remove('active');
        if (loginContainer) loginContainer.classList.add('active');
        if (signupContainer) signupContainer.classList.remove('active');
    } else {
        if (signupTab) signupTab.classList.add('active');
        if (loginTab) loginTab.classList.remove('active');
        if (signupContainer) signupContainer.classList.add('active');
        if (loginContainer) loginContainer.classList.remove('active');
    }
}

// Handle save draft
function handleSaveDraft() {
    try {
        const formData = collectPortfolioData();
        localStorage.setItem('portfolio-draft', JSON.stringify(formData));
        showNotification('Draft saved successfully!', 'success');
    } catch (error) {
        console.error('Error saving draft:', error);
        showNotification('Error saving draft', 'error');
    }
}

// Load draft
function loadDraft() {
    try {
        const draft = localStorage.getItem('portfolio-draft');
        if (draft) {
            const data = JSON.parse(draft);
            populateForm(data);
            showNotification('Draft loaded', 'info');
        }
    } catch (error) {
        console.error('Error loading draft:', error);
    }
}

// Populate form with data
function populateForm(data) {
    if (data.name) document.getElementById('portfolio-name').value = data.name;
    if (data.username) document.getElementById('portfolio-username').value = data.username;
    if (data.bio) document.getElementById('portfolio-bio').value = data.bio;
    if (data.skills) document.getElementById('portfolio-skills').value = data.skills.join(', ');
    if (data.github_url) document.getElementById('portfolio-github').value = data.github_url;
    if (data.linkedin_url) document.getElementById('portfolio-linkedin').value = data.linkedin_url;

    // Update skills preview and char counter
    updateSkillsPreview();
    updateCharCounter();
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);

    // Close button
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// Form validation
function validateForm() {
    const requiredInputs = portfolioForm.querySelectorAll('input[required], textarea[required]');

    for (let input of requiredInputs) {
        if (!input.value.trim()) {
            input.focus();
            showNotification(`Please fill in the ${input.previousElementSibling.textContent.replace(/[^a-zA-Z\s]/g, '').trim()} field.`, 'error');
            return false;
        }
    }

    // Validate skills
    const skills = document.getElementById('portfolio-skills').value.trim();
    if (!skills) {
        showNotification('Please add at least one skill.', 'error');
        return false;
    }

    // Validate projects
    const projects = collectProjects();
    if (projects.length === 0) {
        showNotification('Please add at least one project.', 'error');
        return false;
    }

    return true;
}

// Skills preview
function updateSkillsPreview() {
    const skillsInput = document.getElementById('portfolio-skills');
    const skillsPreview = document.getElementById('skills-preview');

    if (!skillsInput || !skillsPreview) return;

    const skills = skillsInput.value
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill);

    skillsPreview.innerHTML = skills
        .map(skill => `<span class="skill-tag">${skill}</span>`)
        .join('');
}

// Character counter
function updateCharCounter() {
    const bioInput = document.getElementById('portfolio-bio');
    const counter = document.getElementById('bio-counter');

    if (!bioInput || !counter) return;

    const length = bioInput.value.length;
    counter.textContent = length;

    if (length > 500) {
        counter.style.color = 'var(--danger-color)';
        bioInput.value = bioInput.value.substring(0, 500);
    } else {
        counter.style.color = 'var(--text-muted)';
    }
}

// Collect projects data
function collectProjects() {
    const projects = [];
    const projectItems = projectsContainer.querySelectorAll('.project-item');

    projectItems.forEach(item => {
        const title = item.querySelector('.project-title').value.trim();
        const description = item.querySelector('.project-description').value.trim();

        if (title && description) {
            projects.push({ title, description });
        }
    });

    return projects;
}

// Check authentication state
async function checkAuthState() {
    console.log('Checking authentication state...');
    try {
        if (!supabase) {
            console.log('Supabase not initialized, showing auth section');
            showAuthSection();
            return;
        }

        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
            console.log('User is authenticated:', session.user.email);
            currentUser = session.user;
            showPortfolioSection();
        } else {
            console.log('No active session, showing auth section');
            showAuthSection();
        }
    } catch (error) {
        console.error('Error checking auth state:', error);
        showAuthSection();
    }
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    console.log('🔐 Login form submitted');

    const email = document.getElementById('login-email')?.value?.trim();
    const password = document.getElementById('login-password')?.value;
    const errorDiv = document.getElementById('login-error');

    console.log('📧 Login attempt with email:', email);

    // Clear previous errors
    if (errorDiv) {
        errorDiv.textContent = '';
        errorDiv.style.display = 'none';
    }

    // Validation
    if (!email || !password) {
        const message = 'Please fill in all fields';
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        showNotification(message, 'error');
        return;
    }

    if (!email.includes('@')) {
        const message = 'Please enter a valid email address';
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        showNotification(message, 'error');
        return;
    }

    showLoading(true, 'Signing you in...');

    try {
        if (!supabase) {
            throw new Error('Authentication service not available. Please refresh the page.');
        }

        console.log('🚀 Attempting Supabase login...');
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });

        if (error) {
            console.error('❌ Supabase login error:', error);

            // Handle specific error cases
            let userMessage = error.message;
            if (error.message.includes('Invalid login credentials')) {
                userMessage = 'Invalid email or password. Please check your credentials.';
            } else if (error.message.includes('Email not confirmed')) {
                userMessage = 'Please check your email and confirm your account first.';
            } else if (error.message.includes('Too many requests')) {
                userMessage = 'Too many login attempts. Please wait a moment and try again.';
            }

            throw new Error(userMessage);
        }

        if (!data.user) {
            throw new Error('Login failed. No user data received.');
        }

        console.log('✅ Login successful:', data);
        currentUser = data.user;
        hideAuthModal();
        showPortfolioSection();
        clearForm(document.getElementById('login-form'));
        showNotification(`Welcome back, ${data.user.email}! 👋`, 'success');

    } catch (error) {
        console.error('❌ Login error:', error);
        const message = error.message || 'Login failed. Please try again.';
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        showNotification(message, 'error');
    } finally {
        showLoading(false);
    }
}

// Handle signup
async function handleSignup(e) {
    e.preventDefault();
    console.log('📝 Signup form submitted');

    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm').value;
    const errorDiv = document.getElementById('signup-error');

    console.log('📧 Signup attempt with email:', email);

    // Clear previous errors
    if (errorDiv) errorDiv.textContent = '';

    if (!email || !password || !confirmPassword) {
        const message = 'Please fill in all fields';
        if (errorDiv) errorDiv.textContent = message;
        showNotification(message, 'error');
        return;
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
        const message = 'Passwords do not match';
        if (errorDiv) errorDiv.textContent = message;
        showNotification(message, 'error');
        return;
    }

    if (password.length < 6) {
        const message = 'Password must be at least 6 characters';
        if (errorDiv) errorDiv.textContent = message;
        showNotification(message, 'error');
        return;
    }

    showLoading(true, 'Creating your account...');

    try {
        if (!supabase) {
            throw new Error('Supabase not initialized. Please check your configuration.');
        }

        console.log('🚀 Attempting Supabase signup...');
        const { data, error } = await supabase.auth.signUp({
            email,
            password
        });

        if (error) {
            console.error('❌ Supabase signup error:', error);
            throw error;
        }

        console.log('✅ Signup successful:', data);

        if (data.user) {
            currentUser = data.user;
            hideAuthModal();
            showPortfolioSection();
            clearForm(document.getElementById('signup-form'));
            showNotification('Account created successfully! Welcome! 🎉', 'success');
        } else {
            showNotification('Please check your email to confirm your account', 'info');
        }

    } catch (error) {
        console.error('❌ Signup error:', error);
        const message = error.message || 'Signup failed. Please try again.';
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        showNotification(message, 'error');
    } finally {
        showLoading(false);
    }
}

// Handle logout
async function handleLogout() {
    try {
        await supabase.auth.signOut();
        currentUser = null;
        portfolioData = null;
        showAuthSection();
        clearAllForms();
    } catch (error) {
        console.error('Error logging out:', error);
    }
}

// Show authentication section
function showAuthSection() {
    heroSection.classList.remove('hidden');
    authModal.classList.add('hidden');
    portfolioSection.classList.add('hidden');
    previewSection.classList.add('hidden');
    headerActions.classList.remove('hidden');
    userMenu.classList.add('hidden');
}

// Show portfolio section
function showPortfolioSection() {
    heroSection.classList.add('hidden');
    authModal.classList.add('hidden');
    portfolioSection.classList.remove('hidden');
    previewSection.classList.add('hidden');
    headerActions.classList.add('hidden');
    userMenu.classList.remove('hidden');
    userEmail.textContent = currentUser.email;

    // Load draft if available
    loadDraft();
}

// Show preview section
function showPreviewSection() {
    heroSection.classList.add('hidden');
    authModal.classList.add('hidden');
    portfolioSection.classList.add('hidden');
    previewSection.classList.remove('hidden');
    headerActions.classList.add('hidden');
    userMenu.classList.remove('hidden');
}

// Add project field
function addProjectField() {
    const projectCount = projectsContainer.children.length + 1;
    const projectItem = document.createElement('div');
    projectItem.className = 'project-item';
    projectItem.innerHTML = `
        <div class="project-header">
            <h3>Project #${projectCount}</h3>
            <button type="button" class="btn-remove" title="Remove Project">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="form-group">
            <label>Project Title *</label>
            <input type="text" class="project-title" placeholder="My Awesome Project" required>
        </div>

        <div class="form-group">
            <label>Project Description *</label>
            <textarea class="project-description" rows="3" placeholder="Describe your project, technologies used, and key achievements..." required></textarea>
        </div>
    `;
    projectsContainer.appendChild(projectItem);
    updateProjectNumbers();
}

// Remove project field
function removeProjectField(projectItem) {
    if (projectsContainer.children.length > 1) {
        projectItem.remove();
        updateProjectNumbers();
    }
}

// Update project numbers
function updateProjectNumbers() {
    const projectItems = projectsContainer.querySelectorAll('.project-item');
    projectItems.forEach((item, index) => {
        const header = item.querySelector('.project-header h3');
        if (header) {
            header.textContent = `Project #${index + 1}`;
        }
    });
}

// Handle portfolio form submission
async function handlePortfolioSubmit(e) {
    e.preventDefault();

    if (!currentUser) {
        showNotification('Please log in first', 'error');
        return;
    }

    // Validate form
    if (!validateForm()) {
        return;
    }

    showLoading(true, 'Creating your portfolio...');

    try {
        // Collect and validate form data
        portfolioData = collectPortfolioData();

        // Check username uniqueness if provided
        if (portfolioData.username) {
            await checkUsernameAvailability(portfolioData.username);
        }

        // Save to Supabase
        await savePortfolioToDatabase(portfolioData);

        // Clear draft
        localStorage.removeItem('portfolio-draft');

        // Send confirmation email (don't fail if this fails)
        try {
            await sendConfirmationEmail(portfolioData);
        } catch (emailError) {
            console.warn('Failed to send confirmation email:', emailError);
        }

        // Show preview
        renderPortfolioPreview(portfolioData);
        showPreviewSection();
        showNotification('Portfolio created successfully! 🎉', 'success');

    } catch (error) {
        console.error('Error creating portfolio:', error);
        showNotification(error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// Check if username is available
async function checkUsernameAvailability(username) {
    if (!supabase) return;

    const { data, error } = await supabase
        .from('portfolios')
        .select('username')
        .eq('username', username)
        .neq('user_id', currentUser.id)
        .single();

    if (error && error.code !== 'PGRST116') {
        throw error;
    }

    if (data) {
        throw new Error('Username is already taken. Please choose a different one.');
    }
}

// Show form error message
function showFormError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error';
    errorDiv.style.cssText = `
        background: #f8d7da;
        color: #721c24;
        padding: 12px 16px;
        border-radius: 8px;
        margin: 20px 0;
        border: 1px solid #f5c6cb;
        text-align: center;
    `;
    errorDiv.textContent = message;

    const submitButton = portfolioForm.querySelector('button[type="submit"]');
    portfolioForm.insertBefore(errorDiv, submitButton);

    // Scroll to error
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Collect portfolio data from form
function collectPortfolioData() {
    const projects = collectProjects();

    const skills = document.getElementById('portfolio-skills').value
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill);

    // Validate required fields
    const name = document.getElementById('portfolio-name').value.trim();
    const bio = document.getElementById('portfolio-bio').value.trim();

    if (!name) {
        throw new Error('Name is required');
    }

    if (!bio) {
        throw new Error('Bio is required');
    }

    if (skills.length === 0) {
        throw new Error('At least one skill is required');
    }

    if (projects.length === 0) {
        throw new Error('At least one project is required');
    }

    // Validate username if provided
    const username = document.getElementById('portfolio-username').value.trim();
    if (username && !/^[a-zA-Z0-9_-]+$/.test(username)) {
        throw new Error('Username can only contain letters, numbers, hyphens, and underscores');
    }

    // Validate URLs if provided
    const githubUrl = document.getElementById('portfolio-github').value.trim();
    const linkedinUrl = document.getElementById('portfolio-linkedin').value.trim();

    if (githubUrl && !isValidUrl(githubUrl)) {
        throw new Error('Please enter a valid GitHub URL');
    }

    if (linkedinUrl && !isValidUrl(linkedinUrl)) {
        throw new Error('Please enter a valid LinkedIn URL');
    }

    return {
        user_id: currentUser.id,
        name: name,
        username: username || null,
        bio: bio,
        skills: skills,
        github_url: githubUrl || null,
        linkedin_url: linkedinUrl || null,
        projects: projects,
        created_at: new Date().toISOString()
    };
}

// Validate URL format
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Utility functions
function showLoading(show, message = 'Processing...') {
    const loadingElement = document.getElementById('loading');
    const loadingText = document.getElementById('loading-text');

    if (loadingElement) {
        loadingElement.classList.toggle('hidden', !show);
    }

    if (loadingText) {
        loadingText.textContent = message;
    }

    if (show) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

function clearForm(form) {
    if (form) {
        form.reset();
        const errorDiv = form.querySelector('.error-message');
        if (errorDiv) errorDiv.textContent = '';
    }
}

function showSuccessMessage() {
    successMessage.classList.remove('hidden');
    setTimeout(() => {
        successMessage.classList.add('hidden');
    }, 5000);
}

function clearForm(form) {
    form.reset();
    const errorDiv = form.querySelector('.error-message');
    if (errorDiv) errorDiv.textContent = '';
}

function clearAllForms() {
    clearForm(loginForm);
    clearForm(signupForm);
    clearForm(portfolioForm);
}

// Save portfolio to Supabase database
async function savePortfolioToDatabase(data) {
    try {
        // First, check if user already has a portfolio
        const { data: existingPortfolio, error: fetchError } = await supabase
            .from('portfolios')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();

        if (fetchError && fetchError.code !== 'PGRST116') {
            throw fetchError;
        }

        if (existingPortfolio) {
            // Update existing portfolio
            const { error } = await supabase
                .from('portfolios')
                .update(data)
                .eq('user_id', currentUser.id);

            if (error) throw error;
        } else {
            // Insert new portfolio
            const { error } = await supabase
                .from('portfolios')
                .insert([data]);

            if (error) throw error;
        }

        console.log('Portfolio saved successfully');
    } catch (error) {
        console.error('Error saving portfolio:', error);
        throw error;
    }
}

// Send confirmation email using EmailJS v4
async function sendConfirmationEmail(data) {
    try {
        if (typeof emailjs === 'undefined') {
            console.warn('EmailJS not available, skipping email');
            return;
        }

        const templateParams = {
            to_email: currentUser.email,
            user_name: data.name,
            portfolio_url: data.username ?
                `${window.location.origin}?portfolio=${data.username}` :
                `${window.location.origin}?user=${currentUser.id}`,
            skills_count: data.skills.length,
            projects_count: data.projects.length
        };

        await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams);
        console.log('Confirmation email sent successfully');
    } catch (error) {
        console.error('Error sending email:', error);
        // Don't throw error here as email is optional
    }
}

// Render portfolio preview
function renderPortfolioPreview(data) {
    const previewContainer = document.getElementById('portfolio-preview');

    const skillsBadges = data.skills.map(skill =>
        `<span class="skill-badge">${skill}</span>`
    ).join('');

    const socialLinks = [];
    if (data.github_url) {
        socialLinks.push(`<a href="${data.github_url}" target="_blank" class="social-link"><i class="fab fa-github"></i> GitHub</a>`);
    }
    if (data.linkedin_url) {
        socialLinks.push(`<a href="${data.linkedin_url}" target="_blank" class="social-link"><i class="fab fa-linkedin"></i> LinkedIn</a>`);
    }

    const projectCards = data.projects.map(project => `
        <div class="project-card">
            <h4 class="project-title">${project.title}</h4>
            <p class="project-description">${project.description}</p>
        </div>
    `).join('');

    previewContainer.innerHTML = `
        <div class="preview-header-content">
            <h1 class="preview-name">${data.name}</h1>
            <p class="preview-bio">${data.bio}</p>
        </div>

        <div class="skills-container">
            ${skillsBadges}
        </div>

        ${socialLinks.length > 0 ? `
            <div class="social-links">
                ${socialLinks.join('')}
            </div>
        ` : ''}

        ${data.projects.length > 0 ? `
            <div class="projects-section">
                <h3>Projects</h3>
                <div class="projects-grid">
                    ${projectCards}
                </div>
            </div>
        ` : ''}
    `;
}

// Share modal functions
function showShareModal() {
    if (!portfolioData) return;

    const portfolioUrl = portfolioData.username ?
        `${window.location.origin}?portfolio=${portfolioData.username}` :
        `${window.location.origin}?user=${currentUser.id}`;

    const shareUrlInput = document.getElementById('share-url-input');
    const portfolioUrlSpan = document.getElementById('portfolio-url');

    if (shareUrlInput) {
        shareUrlInput.value = portfolioUrl;
    }

    if (portfolioUrlSpan) {
        portfolioUrlSpan.textContent = portfolioData.username ?
            `portfoliobuilder.com/${portfolioData.username}` :
            `portfoliobuilder.com/user/${currentUser.id.substring(0, 8)}`;
    }

    shareModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideShareModal() {
    shareModal.classList.add('hidden');
    document.body.style.overflow = '';
}

function copyPortfolioUrl() {
    const shareUrlInput = document.getElementById('share-url-input');
    if (!shareUrlInput) return;

    shareUrlInput.select();
    document.execCommand('copy');

    // Show success feedback
    const copyBtn = document.getElementById('copy-url');
    const originalText = copyBtn.innerHTML;
    copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
    copyBtn.style.background = 'var(--success-color)';

    setTimeout(() => {
        copyBtn.innerHTML = originalText;
        copyBtn.style.background = '';
    }, 2000);
}



// Enhanced success message
function showSuccessMessage(title = 'Portfolio Created Successfully!', message = 'Check your email for confirmation details') {
    // Remove existing notifications
    document.querySelectorAll('.notification').forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = 'notification success';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <div class="notification-text">
                <h4>${title}</h4>
                <p>${message}</p>
            </div>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Load portfolio by username or user ID (for public viewing)
async function loadPublicPortfolio() {
    const urlParams = new URLSearchParams(window.location.search);
    const username = urlParams.get('portfolio');
    const userId = urlParams.get('user');

    if (!username && !userId) return;

    try {
        showLoading(true);

        let query = supabase.from('portfolios').select('*');

        if (username) {
            query = query.eq('username', username);
        } else {
            query = query.eq('user_id', userId);
        }

        const { data, error } = await query.single();

        if (error) throw error;

        if (data) {
            portfolioData = data;
            renderPortfolioPreview(data);

            // Hide auth and form sections, show only preview
            authSection.classList.add('hidden');
            portfolioSection.classList.add('hidden');
            previewSection.classList.remove('hidden');

            // Hide edit button for public view
            editPortfolioBtn.style.display = 'none';
        }

    } catch (error) {
        console.error('Error loading public portfolio:', error);
        alert('Portfolio not found');
    } finally {
        showLoading(false);
    }
}

// Listen for auth state changes
if (supabase) {
    supabase.auth.onAuthStateChange((event, session) => {
        if (event === 'SIGNED_IN') {
            currentUser = session.user;
            showPortfolioSection();
        } else if (event === 'SIGNED_OUT') {
            currentUser = null;
            portfolioData = null;
            showAuthSection();
        }
    });
}
