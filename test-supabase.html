<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Configuration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Supabase Configuration Test</h1>
        <p>Test your Supabase setup with the provided credentials:</p>
        
        <div class="config-info">
            <h3>Configuration:</h3>
            <p><strong>Project URL:</strong> <span id="project-url">Loading...</span></p>
            <p><strong>Anon Key:</strong> <span id="anon-key">Loading...</span></p>
        </div>

        <div class="test-buttons">
            <button class="test-button" onclick="testConnection()">Test Connection</button>
            <button class="test-button" onclick="testAuth()">Test Authentication</button>
            <button class="test-button" onclick="testSignup()">Test Signup</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        let supabase;
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSupabase();
            displayConfig();
        });
        
        function displayConfig() {
            document.getElementById('project-url').textContent = SUPABASE_URL || 'Not configured';
            document.getElementById('anon-key').textContent = SUPABASE_ANON_KEY ? 
                SUPABASE_ANON_KEY.substring(0, 20) + '...' : 'Not configured';
        }
        
        function initializeSupabase() {
            try {
                if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
                    throw new Error('Supabase configuration missing');
                }
                
                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                addResult('✅ Supabase client initialized successfully', 'success');
                console.log('🟢 Supabase initialized:', supabase);
                
            } catch (error) {
                addResult('❌ Failed to initialize Supabase: ' + error.message, 'error');
                console.error('❌ Supabase initialization failed:', error);
            }
        }
        
        async function testConnection() {
            addResult('🔍 Testing Supabase connection...', 'info');
            
            if (!supabase) {
                addResult('❌ Supabase not initialized', 'error');
                return;
            }
            
            try {
                // Test basic connection by getting session
                const { data, error } = await supabase.auth.getSession();
                
                if (error) {
                    addResult('❌ Connection test failed: ' + error.message, 'error');
                } else {
                    addResult('✅ Connection test successful!\nSession data: ' + JSON.stringify(data, null, 2), 'success');
                }
                
            } catch (error) {
                addResult('❌ Connection test exception: ' + error.message, 'error');
            }
        }
        
        async function testAuth() {
            addResult('🔐 Testing authentication system...', 'info');
            
            if (!supabase) {
                addResult('❌ Supabase not initialized', 'error');
                return;
            }
            
            try {
                // Test with dummy credentials
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'wrongpassword'
                });
                
                if (error) {
                    if (error.message.includes('Invalid login credentials')) {
                        addResult('✅ Authentication system working (correctly rejected invalid credentials)', 'success');
                    } else {
                        addResult('⚠️ Auth test result: ' + error.message, 'info');
                    }
                } else {
                    addResult('✅ Authentication test successful: ' + JSON.stringify(data, null, 2), 'success');
                }
                
            } catch (error) {
                addResult('❌ Authentication test exception: ' + error.message, 'error');
            }
        }
        
        async function testSignup() {
            const email = prompt('Enter test email (will create account):');
            if (!email) return;
            
            const password = prompt('Enter test password (min 6 chars):');
            if (!password || password.length < 6) {
                addResult('❌ Password must be at least 6 characters', 'error');
                return;
            }
            
            addResult('📝 Testing signup with: ' + email, 'info');
            
            if (!supabase) {
                addResult('❌ Supabase not initialized', 'error');
                return;
            }
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password
                });
                
                if (error) {
                    addResult('❌ Signup test failed: ' + error.message, 'error');
                } else {
                    addResult('✅ Signup test successful!\nUser: ' + JSON.stringify(data.user, null, 2), 'success');
                    
                    // Sign out the test user
                    await supabase.auth.signOut();
                    addResult('🚪 Test user signed out', 'info');
                }
                
            } catch (error) {
                addResult('❌ Signup test exception: ' + error.message, 'error');
            }
        }
        
        function addResult(message, type) {
            const results = document.getElementById('results');
            const result = document.createElement('div');
            result.className = 'result ' + type;
            result.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(result);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
