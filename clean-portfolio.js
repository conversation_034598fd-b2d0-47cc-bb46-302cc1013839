// Global variables
let supabase;
let currentUser = null;

// DOM Elements
const authButtons = document.getElementById('auth-buttons');
const userMenu = document.getElementById('user-menu');
const userEmail = document.getElementById('user-email');
const dashboardEmail = document.getElementById('dashboard-email');
const heroSection = document.getElementById('hero-section');
const dashboardSection = document.getElementById('dashboard-section');
const authModal = document.getElementById('auth-modal');
const loading = document.getElementById('loading');

// Initialize Supabase
function initSupabase() {
    console.log('🔧 Initializing Supabase...');
    
    if (!window.SUPABASE_URL || !window.SUPABASE_ANON_KEY) {
        console.error('❌ Supabase configuration missing');
        return false;
    }
    
    try {
        supabase = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
        console.log('✅ Supabase initialized successfully');
        console.log('📍 URL:', window.SUPABASE_URL);
        return true;
    } catch (error) {
        console.error('❌ Supabase initialization failed:', error);
        return false;
    }
}

// Utility Functions
function showLoading(show = true) {
    if (show) {
        loading.classList.add('show');
    } else {
        loading.classList.remove('show');
    }
}

function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
}

function hideError(elementId) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.classList.remove('show');
        errorElement.textContent = '';
    }
}

// Modal Functions
function showModal() {
    authModal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function hideModal() {
    authModal.classList.remove('show');
    document.body.style.overflow = '';
}

function switchTab(tabName) {
    console.log('🔄 Switching to tab:', tabName);
    
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    const activeTab = document.getElementById(tabName + '-tab');
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Update modal header
    const modalTitle = document.getElementById('modal-title');
    const modalSubtitle = document.getElementById('modal-subtitle');
    
    if (tabName === 'login') {
        modalTitle.textContent = 'Welcome back';
        modalSubtitle.textContent = 'Sign in to your account';
    } else {
        modalTitle.textContent = 'Create account';
        modalSubtitle.textContent = 'Start building your portfolio today';
    }
}

// UI State Management
function showAuthenticatedState(user) {
    console.log('🟢 User authenticated:', user.email);
    
    // Hide auth buttons, show user menu
    authButtons.classList.add('hidden');
    userMenu.classList.remove('hidden');
    
    // Update email displays
    userEmail.textContent = user.email;
    dashboardEmail.textContent = user.email;
    
    // Show dashboard, hide hero
    heroSection.classList.add('hidden');
    dashboardSection.classList.remove('hidden');
    
    currentUser = user;
}

function showUnauthenticatedState() {
    console.log('🔴 User not authenticated');
    
    // Show auth buttons, hide user menu
    authButtons.classList.remove('hidden');
    userMenu.classList.add('hidden');
    
    // Show hero, hide dashboard
    heroSection.classList.remove('hidden');
    dashboardSection.classList.add('hidden');
    
    currentUser = null;
}

// Authentication Functions
async function handleLogin(e) {
    e.preventDefault();
    console.log('🔐 Login form submitted');
    
    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;
    
    console.log('📧 Login attempt with email:', email);
    
    // Clear previous errors
    hideError('login-error');
    
    // Validation
    if (!email || !password) {
        showError('login-error', 'Please fill in all fields');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            console.error('❌ Login error:', error.message);
            showError('login-error', error.message);
            return;
        }
        
        console.log('🟢 Login success');
        console.log('User data:', data.user);
        
        // Hide modal and show authenticated state
        hideModal();
        showAuthenticatedState(data.user);
        
        // Clear form
        document.getElementById('login-form').reset();
        
    } catch (error) {
        console.error('❌ Login exception:', error);
        showError('login-error', 'An unexpected error occurred');
    } finally {
        showLoading(false);
    }
}

async function handleSignup(e) {
    e.preventDefault();
    console.log('📝 Signup form submitted');
    
    const email = document.getElementById('signup-email').value.trim();
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm').value;
    
    console.log('📧 Signup attempt with email:', email);
    
    // Clear previous errors
    hideError('signup-error');
    
    // Validation
    if (!email || !password || !confirmPassword) {
        showError('signup-error', 'Please fill in all fields');
        return;
    }
    
    if (password !== confirmPassword) {
        showError('signup-error', 'Passwords do not match');
        return;
    }
    
    if (password.length < 6) {
        showError('signup-error', 'Password must be at least 6 characters');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password,
            options: {
                emailRedirectTo: window.location.origin
            }
        });
        
        if (error) {
            console.error('❌ Signup error:', error.message);
            showError('signup-error', error.message);
            return;
        }
        
        console.log('🟢 Signup success');
        console.log('User data:', data.user);
        
        // Hide modal
        hideModal();
        
        // Check if email confirmation is required
        if (data.user && !data.user.email_confirmed_at) {
            alert('Please check your email to confirm your account before signing in.');
            switchTab('login');
            showModal();
        } else {
            showAuthenticatedState(data.user);
        }
        
        // Clear form
        document.getElementById('signup-form').reset();
        
    } catch (error) {
        console.error('❌ Signup exception:', error);
        showError('signup-error', 'An unexpected error occurred');
    } finally {
        showLoading(false);
    }
}

async function handleLogout() {
    console.log('🚪 Logout initiated');
    
    showLoading(true);
    
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            console.error('❌ Logout error:', error);
            return;
        }
        
        console.log('🟢 Logout success');
        showUnauthenticatedState();
        
    } catch (error) {
        console.error('❌ Logout exception:', error);
    } finally {
        showLoading(false);
    }
}

// Check Authentication State
async function checkAuthState() {
    console.log('🔍 Checking authentication state...');
    
    try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
            console.error('❌ Auth state check error:', error);
            showUnauthenticatedState();
            return;
        }
        
        if (session && session.user) {
            console.log('✅ User is authenticated:', session.user.email);
            showAuthenticatedState(session.user);
        } else {
            console.log('ℹ️ No active session');
            showUnauthenticatedState();
        }
        
    } catch (error) {
        console.error('❌ Auth state check exception:', error);
        showUnauthenticatedState();
    }
}

// Event Listeners
function setupEventListeners() {
    console.log('🎯 Setting up event listeners...');
    
    // Auth buttons
    document.getElementById('login-btn').addEventListener('click', () => {
        console.log('🔘 Login button clicked');
        switchTab('login');
        showModal();
    });
    
    document.getElementById('signup-btn').addEventListener('click', () => {
        console.log('🔘 Signup button clicked');
        switchTab('signup');
        showModal();
    });
    
    document.getElementById('get-started-btn').addEventListener('click', () => {
        console.log('🔘 Get started button clicked');
        switchTab('signup');
        showModal();
    });
    
    document.getElementById('logout-btn').addEventListener('click', handleLogout);
    
    // Modal close
    document.getElementById('modal-close').addEventListener('click', hideModal);
    
    // Modal overlay click
    authModal.addEventListener('click', (e) => {
        if (e.target === authModal) {
            hideModal();
        }
    });
    
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            switchTab(btn.dataset.tab);
        });
    });
    
    // Form submissions
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    document.getElementById('signup-form').addEventListener('submit', handleSignup);
    
    console.log('✅ Event listeners setup complete');
}

// Initialize App
async function initApp() {
    console.log('🚀 Initializing Clean Portfolio App...');
    
    // Initialize Supabase
    if (!initSupabase()) {
        console.error('❌ Failed to initialize Supabase');
        return;
    }
    
    // Setup event listeners
    setupEventListeners();
    
    // Check initial auth state
    await checkAuthState();
    
    // Listen for auth state changes
    supabase.auth.onAuthStateChange((event, session) => {
        console.log('🔄 Auth state changed:', event);
        
        if (event === 'SIGNED_IN' && session) {
            showAuthenticatedState(session.user);
        } else if (event === 'SIGNED_OUT') {
            showUnauthenticatedState();
        }
    });
    
    console.log('✅ Clean Portfolio App initialized successfully');
}

// Start the app
document.addEventListener('DOMContentLoaded', initApp);

// Global test functions
window.testAuth = async function() {
    console.log('🧪 Testing authentication...');
    switchTab('signup');
    showModal();
};

window.testLogin = async function() {
    console.log('🧪 Testing login...');
    switchTab('login');
    showModal();
};
