// Global variables
let supabase;
let currentUser = null;

// DOM Elements
const authButtons = document.getElementById('auth-buttons');
const userMenu = document.getElementById('user-menu');
const userEmail = document.getElementById('user-email');
const dashboardEmail = document.getElementById('dashboard-email');
const landingSection = document.getElementById('landing-section');
const dashboardSection = document.getElementById('dashboard-section');
const loginModal = document.getElementById('login-modal');
const signupModal = document.getElementById('signup-modal');
const loading = document.getElementById('loading');

// Button elements
const loginBtn = document.getElementById('login-btn');
const signupBtn = document.getElementById('signup-btn');
const getStartedBtn = document.getElementById('get-started-btn');
const logoutBtn = document.getElementById('logout-btn');
const loginClose = document.getElementById('login-close');
const signupClose = document.getElementById('signup-close');

// Form elements
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');
const loginError = document.getElementById('login-error');
const signupError = document.getElementById('signup-error');

// Initialize Supabase
function initializeSupabase() {
    console.log('🔧 Initializing Supabase...');

    if (typeof SUPABASE_URL === 'undefined' || typeof SUPABASE_ANON_KEY === 'undefined') {
        console.error('❌ Supabase configuration missing. Please check config.js');
        return false;
    }

    try {
        // Use the global supabase object from the CDN
        supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('✅ Supabase initialized successfully');
        console.log('Supabase URL:', SUPABASE_URL);
        return true;
    } catch (error) {
        console.error('❌ Failed to initialize Supabase:', error);
        return false;
    }
}

// Show/Hide Loading
function showLoading(show = true) {
    if (show) {
        loading.classList.remove('hidden');
    } else {
        loading.classList.add('hidden');
    }
}

// Show/Hide Error Messages
function showError(element, message) {
    if (element) {
        element.textContent = message;
        element.classList.remove('hidden');
    }
}

function hideError(element) {
    if (element) {
        element.classList.add('hidden');
        element.textContent = '';
    }
}

// Modal Functions
function showModal(modal) {
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideModal(modal) {
    modal.classList.add('hidden');
    document.body.style.overflow = '';
}

// UI State Management
function showAuthenticatedState(user) {
    console.log('🟢 Showing authenticated state for:', user.email);
    
    // Hide auth buttons, show user menu
    authButtons.classList.add('hidden');
    userMenu.classList.remove('hidden');
    
    // Update user email displays
    userEmail.textContent = user.email;
    dashboardEmail.textContent = user.email;
    
    // Show dashboard, hide landing
    landingSection.classList.add('hidden');
    dashboardSection.classList.remove('hidden');
    
    currentUser = user;
}

function showUnauthenticatedState() {
    console.log('🔴 Showing unauthenticated state');
    
    // Show auth buttons, hide user menu
    authButtons.classList.remove('hidden');
    userMenu.classList.add('hidden');
    
    // Show landing, hide dashboard
    landingSection.classList.remove('hidden');
    dashboardSection.classList.add('hidden');
    
    currentUser = null;
}

// Authentication Functions
async function handleLogin(e) {
    e.preventDefault();
    console.log('🔐 Login form submitted');
    
    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;
    
    console.log('📧 Attempting login with email:', email);
    
    // Clear previous errors
    hideError(loginError);
    
    // Validation
    if (!email || !password) {
        showError(loginError, 'Please fill in all fields');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            console.error('❌ Login error:', error.message);
            showError(loginError, error.message);
            return;
        }
        
        console.log('🟢 Login success');
        console.log('User data:', data.user);
        
        // Hide modal and show authenticated state
        hideModal(loginModal);
        showAuthenticatedState(data.user);
        
        // Clear form
        loginForm.reset();
        
    } catch (error) {
        console.error('❌ Login exception:', error);
        showError(loginError, 'An unexpected error occurred');
    } finally {
        showLoading(false);
    }
}

async function handleSignup(e) {
    e.preventDefault();
    console.log('📝 Signup form submitted');
    
    const email = document.getElementById('signup-email').value.trim();
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm').value;
    
    console.log('📧 Attempting signup with email:', email);
    
    // Clear previous errors
    hideError(signupError);
    
    // Validation
    if (!email || !password || !confirmPassword) {
        showError(signupError, 'Please fill in all fields');
        return;
    }
    
    if (password !== confirmPassword) {
        showError(signupError, 'Passwords do not match');
        return;
    }
    
    if (password.length < 6) {
        showError(signupError, 'Password must be at least 6 characters');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password,
            options: {
                emailRedirectTo: window.location.origin
            }
        });
        
        if (error) {
            console.error('❌ Signup error:', error.message);
            showError(signupError, error.message);
            return;
        }
        
        console.log('🟢 Signup success');
        console.log('User data:', data.user);
        
        // Hide modal
        hideModal(signupModal);
        
        // Check if email confirmation is required
        if (data.user && !data.user.email_confirmed_at) {
            alert('Please check your email to confirm your account before signing in.');
            showModal(loginModal);
        } else {
            showAuthenticatedState(data.user);
        }
        
        // Clear form
        signupForm.reset();
        
    } catch (error) {
        console.error('❌ Signup exception:', error);
        showError(signupError, 'An unexpected error occurred');
    } finally {
        showLoading(false);
    }
}

async function handleLogout() {
    console.log('🚪 Logout initiated');
    
    showLoading(true);
    
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            console.error('❌ Logout error:', error);
            return;
        }
        
        console.log('🟢 Logout success');
        showUnauthenticatedState();
        
    } catch (error) {
        console.error('❌ Logout exception:', error);
    } finally {
        showLoading(false);
    }
}

// Check Authentication State
async function checkAuthState() {
    console.log('🔍 Checking authentication state...');
    
    try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
            console.error('❌ Auth state check error:', error);
            showUnauthenticatedState();
            return;
        }
        
        if (session && session.user) {
            console.log('✅ User is authenticated:', session.user.email);
            showAuthenticatedState(session.user);
        } else {
            console.log('ℹ️ No active session');
            showUnauthenticatedState();
        }
        
    } catch (error) {
        console.error('❌ Auth state check exception:', error);
        showUnauthenticatedState();
    }
}

// Event Listeners
function setupEventListeners() {
    console.log('🎯 Setting up event listeners...');
    
    // Button clicks
    loginBtn.addEventListener('click', () => {
        console.log('🔘 Login button clicked');
        showModal(loginModal);
    });
    
    signupBtn.addEventListener('click', () => {
        console.log('🔘 Signup button clicked');
        showModal(signupModal);
    });
    
    getStartedBtn.addEventListener('click', () => {
        console.log('🔘 Get started button clicked');
        showModal(signupModal);
    });
    
    logoutBtn.addEventListener('click', handleLogout);
    
    // Modal close buttons
    loginClose.addEventListener('click', () => hideModal(loginModal));
    signupClose.addEventListener('click', () => hideModal(signupModal));
    
    // Modal overlay clicks
    loginModal.querySelector('.modal-overlay').addEventListener('click', () => hideModal(loginModal));
    signupModal.querySelector('.modal-overlay').addEventListener('click', () => hideModal(signupModal));
    
    // Form submissions
    loginForm.addEventListener('submit', handleLogin);
    signupForm.addEventListener('submit', handleSignup);
    
    console.log('✅ Event listeners setup complete');
}

// Initialize App
async function initializeApp() {
    console.log('🚀 Initializing Portfolio App...');
    
    // Initialize Supabase
    if (!initializeSupabase()) {
        console.error('❌ Failed to initialize Supabase');
        return;
    }
    
    // Setup event listeners
    setupEventListeners();
    
    // Check initial auth state
    await checkAuthState();
    
    // Listen for auth state changes
    supabase.auth.onAuthStateChange((event, session) => {
        console.log('🔄 Auth state changed:', event);
        
        if (event === 'SIGNED_IN' && session) {
            showAuthenticatedState(session.user);
        } else if (event === 'SIGNED_OUT') {
            showUnauthenticatedState();
        }
    });
    
    console.log('✅ Portfolio App initialized successfully');
}

// Test Functions (for debugging)
window.testAuth = async function() {
    console.log('🧪 Testing authentication...');

    if (!supabase) {
        console.error('❌ Supabase not initialized');
        return;
    }

    try {
        // Test signup with a unique email
        const testEmail = `test${Date.now()}@example.com`;
        const testPassword = 'test123456';

        console.log('📧 Testing signup with:', testEmail);

        const { data, error } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword
        });

        if (error) {
            console.error('❌ Test signup error:', error.message);
        } else {
            console.log('🟢 Test signup success:', data);
        }

    } catch (error) {
        console.error('❌ Test auth exception:', error);
    }
};

window.testLogin = async function() {
    console.log('🧪 Testing login...');

    if (!supabase) {
        console.error('❌ Supabase not initialized');
        return;
    }

    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'test123456'
        });

        if (error) {
            console.error('❌ Test login error:', error.message);
        } else {
            console.log('🟢 Test login success:', data);
        }

    } catch (error) {
        console.error('❌ Test login exception:', error);
    }
};

// Start the app when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);
