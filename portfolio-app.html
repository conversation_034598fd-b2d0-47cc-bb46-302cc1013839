<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio App</title>
    <link rel="stylesheet" href="portfolio-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav">
                <div class="logo">
                    <i class="fas fa-briefcase"></i>
                    <span>Portfolio</span>
                </div>
                <div class="nav-actions">
                    <!-- Show when not authenticated -->
                    <div id="auth-buttons" class="auth-buttons">
                        <button id="login-btn" class="btn btn-outline">Sign In</button>
                        <button id="signup-btn" class="btn btn-primary">Sign Up</button>
                    </div>
                    <!-- Show when authenticated -->
                    <div id="user-menu" class="user-menu hidden">
                        <span id="user-email" class="user-email"></span>
                        <button id="logout-btn" class="btn btn-outline">Logout</button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Landing Section (shown when not authenticated) -->
        <section id="landing-section" class="landing-section">
            <div class="container">
                <div class="hero">
                    <h1>Create Your Professional Portfolio</h1>
                    <p>Build stunning portfolios that showcase your skills and projects</p>
                    <button id="get-started-btn" class="btn btn-primary btn-large">Get Started</button>
                </div>
            </div>
        </section>

        <!-- Dashboard Section (shown when authenticated) -->
        <section id="dashboard-section" class="dashboard-section hidden">
            <div class="container">
                <div class="dashboard">
                    <h1>Welcome to Your Dashboard</h1>
                    <p id="welcome-message">Logged in as: <span id="dashboard-email"></span></p>
                    <div class="dashboard-content">
                        <div class="card">
                            <h3>Your Portfolio</h3>
                            <p>Start building your professional portfolio</p>
                            <button class="btn btn-primary">Create Portfolio</button>
                        </div>
                        <div class="card">
                            <h3>Projects</h3>
                            <p>Manage your projects and showcase your work</p>
                            <button class="btn btn-outline">Add Project</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Login Modal -->
    <div id="login-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sign In</h2>
                <button id="login-close" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="login-form" class="auth-form">
                <div class="form-group">
                    <label for="login-email">Email</label>
                    <input type="email" id="login-email" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" required placeholder="Enter your password">
                </div>
                <button type="submit" class="btn btn-primary btn-full">Sign In</button>
                <div id="login-error" class="error-message hidden"></div>
            </form>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signup-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sign Up</h2>
                <button id="signup-close" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="signup-form" class="auth-form">
                <div class="form-group">
                    <label for="signup-email">Email</label>
                    <input type="email" id="signup-email" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <input type="password" id="signup-password" required placeholder="Create a password" minlength="6">
                </div>
                <div class="form-group">
                    <label for="signup-confirm">Confirm Password</label>
                    <input type="password" id="signup-confirm" required placeholder="Confirm your password" minlength="6">
                </div>
                <button type="submit" class="btn btn-primary btn-full">Sign Up</button>
                <div id="signup-error" class="error-message hidden"></div>
            </form>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="portfolio-app.js"></script>
</body>
</html>
