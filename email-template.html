<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Created Successfully</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
        }
        .header h1 {
            color: #4CAF50;
            margin: 0;
            font-size: 28px;
        }
        .content {
            margin-bottom: 30px;
        }
        .stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .stats h3 {
            margin-top: 0;
            color: #333;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .cta-button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 14px;
        }
        .success-icon {
            font-size: 48px;
            color: #4CAF50;
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✅</div>
            <h1>Portfolio Created Successfully!</h1>
        </div>
        
        <div class="content">
            <p>Hi <strong>{{user_name}}</strong>,</p>
            
            <p>Congratulations! Your professional portfolio has been created successfully and is now live.</p>
            
            <div class="stats">
                <h3>Portfolio Summary</h3>
                <div class="stat-item">
                    <span>Skills Added:</span>
                    <strong>{{skills_count}}</strong>
                </div>
                <div class="stat-item">
                    <span>Projects Showcased:</span>
                    <strong>{{projects_count}}</strong>
                </div>
                <div class="stat-item">
                    <span>Status:</span>
                    <strong style="color: #4CAF50;">Live & Ready to Share</strong>
                </div>
            </div>
            
            <p>Your portfolio is now accessible at the following URL:</p>
            
            <div style="text-align: center;">
                <a href="{{portfolio_url}}" class="cta-button">View Your Portfolio</a>
            </div>
            
            <p>You can share this link with potential employers, clients, or anyone who wants to learn more about your professional background and skills.</p>
            
            <h3>What's Next?</h3>
            <ul>
                <li>Share your portfolio URL on social media</li>
                <li>Add the link to your email signature</li>
                <li>Include it in your resume and job applications</li>
                <li>Update your portfolio anytime by logging back in</li>
            </ul>
            
            <p>Thank you for using Portfolio Builder! We're excited to see your professional journey unfold.</p>
        </div>
        
        <div class="footer">
            <p>This email was sent because you created a portfolio on Portfolio Builder.</p>
            <p>If you have any questions or need support, please don't hesitate to reach out.</p>
        </div>
    </div>
</body>
</html>

<!-- 
EmailJS Template Variables to use:
- {{to_email}} - Recipient's email address
- {{user_name}} - User's full name
- {{portfolio_url}} - Direct link to the portfolio
- {{skills_count}} - Number of skills added
- {{projects_count}} - Number of projects added

Instructions for EmailJS setup:
1. Copy this HTML content
2. Go to EmailJS dashboard > Email Templates
3. Create a new template
4. Paste this HTML in the content area
5. Make sure the template ID matches the one in script.js
6. Test the template with sample data
-->
