# Quick Setup Guide for Your Portfolio Builder

Your Supabase credentials have been configured! Here's what you need to do to complete the setup:

## ✅ Supabase Configuration (DONE)
- **Project ID**: lwmqufwuzmmruecyaxgg
- **Project URL**: https://lwmqufwuzmmruecyaxgg.supabase.co
- **Anon Key**: Configured in `config.js`

## 🗄️ Database Setup (REQUIRED)

1. **Go to your Supabase Dashboard**:
   - Visit: https://supabase.com/dashboard/project/lwmqufwuzmmruecyaxgg
   - Login with your Supabase account

2. **Run the Database Setup**:
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"
   - Copy and paste the contents of `supabase-setup.sql`
   - Click "Run" to execute the SQL

3. **Verify Setup**:
   - Go to "Table Editor" in the left sidebar
   - You should see a "portfolios" table
   - The table should have columns: id, user_id, name, username, bio, skills, github_url, linkedin_url, projects, created_at, updated_at

## 📧 EmailJS Setup (OPTIONAL)

EmailJS is optional but recommended for confirmation emails. If you want to set it up:

1. **Create EmailJS Account**:
   - Go to https://www.emailjs.com/
   - Sign up for a free account

2. **Create Email Service**:
   - Add an email service (Gmail, Outlook, etc.)
   - Note down the Service ID

3. **Create Email Template**:
   - Create a new template
   - Use the content from `email-template.html`
   - Note down the Template ID

4. **Get User ID**:
   - Go to Account > General
   - Copy your Public Key (User ID)

5. **Update Configuration**:
   - Edit `config.js`
   - Replace the EmailJS placeholders with your actual IDs

## 🚀 Test Your Application

1. **Start the Server** (if not already running):
   ```bash
   python -m http.server 8000
   ```

2. **Open in Browser**:
   - Go to http://localhost:8000
   - You should see the Portfolio Builder app

3. **Test the Features**:
   - Create a new account (sign up)
   - Build a portfolio with your information
   - Check that data is saved in Supabase
   - Test the portfolio preview and sharing

## 🔧 Troubleshooting

### If you see "Configuration Error":
- Make sure you ran the SQL setup in Supabase
- Check that your Supabase project is active
- Verify the credentials in `config.js`

### If authentication doesn't work:
- Go to Supabase Dashboard > Authentication > Settings
- Make sure "Enable email confirmations" is turned OFF for testing
- Check that the site URL is set correctly

### If data isn't saving:
- Check the browser console for errors
- Verify the database table was created correctly
- Make sure RLS policies are set up properly

## 📱 Deploy Your App

Once everything works locally, you can deploy to:

- **Netlify**: Drag and drop your folder
- **Vercel**: Connect your GitHub repository  
- **GitHub Pages**: Push to GitHub and enable Pages

## 🎉 You're Ready!

Your portfolio builder is now configured and ready to use. The app includes:

- ✅ User authentication
- ✅ Portfolio creation form
- ✅ Database storage
- ✅ Live preview
- ✅ Public sharing
- ✅ Responsive design
- ⚠️ Email notifications (if you set up EmailJS)

Happy portfolio building! 🚀
