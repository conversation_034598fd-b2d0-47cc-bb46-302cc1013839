<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Builder - Professional Platform</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-900: #1e3a8a;
            
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            
            --success-50: #ecfdf5;
            --success-500: #10b981;
            --success-600: #059669;
            
            --error-50: #fef2f2;
            --error-500: #ef4444;
            --error-600: #dc2626;
            
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-600);
            text-decoration: none;
        }

        .logo i {
            font-size: 1.75rem;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-600);
            border: 2px solid var(--primary-500);
        }

        .btn-outline:hover {
            background: var(--primary-500);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-large {
            padding: 1.25rem 2.5rem;
            font-size: 1.125rem;
            border-radius: var(--radius-xl);
        }

        /* Hero Section */
        .hero {
            position: relative;
            text-align: center;
            padding: 6rem 0 8rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.375rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 400;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-hero {
            padding: 1.25rem 2.5rem;
            font-size: 1.125rem;
            border-radius: var(--radius-xl);
            font-weight: 600;
        }

        .btn-hero-primary {
            background: rgba(255, 255, 255, 0.95);
            color: var(--primary-700);
            backdrop-filter: blur(10px);
        }

        .btn-hero-primary:hover {
            background: white;
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .btn-hero-outline {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-hero-outline:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .feature-card {
            text-align: center;
            padding: 2.5rem;
            border-radius: var(--radius-2xl);
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-200);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .feature-card p {
            color: var(--gray-600);
            line-height: 1.7;
        }

        /* Dashboard */
        .dashboard {
            padding: 6rem 0;
            background: var(--gray-50);
            display: none;
        }

        .dashboard.show {
            display: block;
        }

        .dashboard h2 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--gray-900);
            text-align: center;
        }

        .dashboard-subtitle {
            text-align: center;
            color: var(--gray-600);
            font-size: 1.25rem;
            margin-bottom: 4rem;
        }

        .user-info {
            background: white;
            padding: 3rem;
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            margin: 0 auto;
            max-width: 800px;
            text-align: center;
            border: 1px solid var(--gray-200);
        }

        .user-avatar {
            width: 5rem;
            height: 5rem;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            font-weight: 600;
        }

        .user-info h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .user-email-display {
            color: var(--primary-600);
            font-weight: 500;
            margin-bottom: 2rem;
        }

        /* Section Headers */
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            display: flex;
            opacity: 1;
        }

        .modal-content {
            background: white;
            border-radius: var(--radius-2xl);
            width: 100%;
            max-width: 480px;
            position: relative;
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 2.5rem 2.5rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid var(--gray-200);
            position: relative;
            background: linear-gradient(135deg, var(--primary-50), var(--gray-50));
            border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
        }

        .modal-header h2 {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .modal-header p {
            color: var(--gray-600);
            font-size: 1rem;
        }

        .modal-close {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            background: var(--gray-100);
            border: none;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--gray-500);
            font-size: 1.25rem;
        }

        .modal-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
            transform: rotate(90deg);
        }

        .modal-tabs {
            display: flex;
            background: var(--gray-50);
            border-radius: 0;
            margin: 0 2.5rem;
            border-radius: var(--radius-lg);
            padding: 0.25rem;
            margin-top: -0.5rem;
            position: relative;
            z-index: 10;
        }

        .tab-btn {
            flex: 1;
            padding: 0.875rem 1.5rem;
            background: none;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: var(--primary-700);
            background: white;
            box-shadow: var(--shadow-sm);
        }

        .modal-body {
            padding: 2.5rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: var(--gray-800);
            font-size: 0.875rem;
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
            color: var(--gray-800);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-500);
            background: white;
            box-shadow: 0 0 0 4px var(--primary-50);
            transform: translateY(-1px);
        }

        .form-group input::placeholder {
            color: var(--gray-400);
        }

        .btn-full {
            width: 100%;
            margin-top: 1.5rem;
            padding: 1rem 1.5rem;
            font-size: 1rem;
        }

        .message {
            margin-top: 1.5rem;
            padding: 1rem 1.25rem;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
            display: none;
            border: 1px solid;
        }

        .message.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .error-message {
            color: var(--error-600);
            background: var(--error-50);
            border-color: var(--error-200);
        }

        .success-message {
            color: var(--success-600);
            background: var(--success-50);
            border-color: var(--success-200);
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(4px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            color: white;
        }

        .loading.show {
            display: flex;
        }

        .loading-content {
            text-align: center;
        }

        .spinner {
            width: 3rem;
            height: 3rem;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.125rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-hero {
                width: 100%;
                max-width: 300px;
            }

            .nav-buttons {
                gap: 0.5rem;
            }

            .btn {
                padding: 0.625rem 1.25rem;
                font-size: 0.8rem;
            }

            .modal-content {
                margin: 1rem;
                max-width: none;
            }

            .modal-header,
            .modal-body {
                padding: 2rem 1.5rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .section-title {
                font-size: 2.5rem;
            }

            .dashboard h2 {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .hero {
                padding: 4rem 0 6rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .container {
                padding: 0 1rem;
            }

            .modal-header,
            .modal-body {
                padding: 1.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="#" class="logo">
                    <i class="fas fa-rocket"></i>
                    PortfolioBuilder
                </a>
                <div class="nav-buttons">
                    <div id="auth-buttons">
                        <button id="login-btn" class="btn btn-outline">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                        <button id="signup-btn" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i>
                            Get Started
                        </button>
                    </div>
                    <div id="user-menu" class="hidden">
                        <span id="user-email" style="margin-right: 1rem; color: var(--gray-600); font-weight: 500;"></span>
                        <button id="logout-btn" class="btn btn-outline">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero-section" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Create Your Professional Portfolio</h1>
                <p>Build stunning, responsive portfolios that showcase your skills, projects, and achievements. Stand out from the crowd with our modern, professional templates.</p>
                <div class="hero-buttons">
                    <button id="get-started-btn" class="btn btn-hero btn-hero-primary">
                        <i class="fas fa-rocket"></i>
                        Start Building Now
                    </button>
                    <button class="btn btn-hero btn-hero-outline">
                        <i class="fas fa-play"></i>
                        Watch Demo
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose PortfolioBuilder?</h2>
                <p class="section-subtitle">Everything you need to create a professional portfolio that gets you noticed</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Beautiful Templates</h3>
                    <p>Choose from professionally designed templates that are modern, responsive, and customizable to match your personal brand.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Responsive</h3>
                    <p>Your portfolio looks perfect on all devices. From desktop to mobile, your work is showcased beautifully everywhere.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Lightning Fast</h3>
                    <p>Built with performance in mind. Your portfolio loads instantly, keeping visitors engaged and impressed.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Section -->
    <section id="dashboard-section" class="dashboard">
        <div class="container">
            <h2>Welcome to Your Dashboard</h2>
            <p class="dashboard-subtitle">Start building your professional portfolio</p>
            <div class="user-info">
                <div class="user-avatar" id="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h3>Welcome back!</h3>
                <p class="user-email-display">Logged in as: <span id="dashboard-email"></span></p>
                <div class="hero-buttons">
                    <button class="btn btn-primary btn-large">
                        <i class="fas fa-plus"></i>
                        Create New Portfolio
                    </button>
                    <button class="btn btn-outline btn-large">
                        <i class="fas fa-folder-open"></i>
                        View My Portfolios
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="modal">
        <div class="modal-content">
            <button class="modal-close" id="modal-close">
                <i class="fas fa-times"></i>
            </button>

            <div class="modal-header">
                <h2 id="modal-title">Welcome</h2>
                <p id="modal-subtitle">Sign in to your account</p>
            </div>

            <div class="modal-tabs">
                <button class="tab-btn active" data-tab="login">Sign In</button>
                <button class="tab-btn" data-tab="signup">Sign Up</button>
            </div>

            <div class="modal-body">
                <!-- Login Form -->
                <div id="login-tab" class="tab-content active">
                    <form id="login-form">
                        <div class="form-group">
                            <label for="login-email">
                                <i class="fas fa-envelope" style="margin-right: 0.5rem; color: var(--primary-500);"></i>
                                Email Address
                            </label>
                            <input type="email" id="login-email" name="email" required placeholder="Enter your email address" autocomplete="email">
                        </div>
                        <div class="form-group">
                            <label for="login-password">
                                <i class="fas fa-lock" style="margin-right: 0.5rem; color: var(--primary-500);"></i>
                                Password
                            </label>
                            <input type="password" id="login-password" name="password" required placeholder="Enter your password" autocomplete="current-password">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                        <div id="login-error" class="message error-message"></div>
                        <div id="login-success" class="message success-message"></div>
                    </form>
                </div>

                <!-- Signup Form -->
                <div id="signup-tab" class="tab-content">
                    <form id="signup-form">
                        <div class="form-group">
                            <label for="signup-email">
                                <i class="fas fa-envelope" style="margin-right: 0.5rem; color: var(--primary-500);"></i>
                                Email Address
                            </label>
                            <input type="email" id="signup-email" name="email" required placeholder="Enter your email address" autocomplete="email">
                        </div>
                        <div class="form-group">
                            <label for="signup-password">
                                <i class="fas fa-lock" style="margin-right: 0.5rem; color: var(--primary-500);"></i>
                                Password
                            </label>
                            <input type="password" id="signup-password" name="password" required placeholder="Create a password (min 6 characters)" minlength="6" autocomplete="new-password">
                        </div>
                        <div class="form-group">
                            <label for="signup-confirm">
                                <i class="fas fa-shield-alt" style="margin-right: 0.5rem; color: var(--primary-500);"></i>
                                Confirm Password
                            </label>
                            <input type="password" id="signup-confirm" name="confirm-password" required placeholder="Confirm your password" minlength="6" autocomplete="new-password">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>
                        <div id="signup-error" class="message error-message"></div>
                        <div id="signup-success" class="message success-message"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading" class="loading">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Inline Supabase configuration
        const SUPABASE_CONFIG = {
            URL: 'https://lwmqufwuzmmruecyaxgg.supabase.co',
            ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.H2YHgnBv7lWNqDfxfhKojHsAN6Fy2Yrlj6ul94Jvj_Q'
        };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="professional-portfolio.js"></script>
</body>
</html>
