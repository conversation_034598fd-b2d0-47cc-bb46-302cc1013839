<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Builder</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.emailjs.com/dist/email.min.js"></script>
    <script src="config.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Portfolio Builder</h1>
            <p>Create your professional portfolio in minutes</p>
        </header>

        <!-- Authentication Section -->
        <section id="auth-section" class="auth-section">
            <div class="auth-container">
                <div class="auth-tabs">
                    <button id="login-tab" class="tab-button active">Login</button>
                    <button id="signup-tab" class="tab-button">Sign Up</button>
                </div>

                <!-- Login Form -->
                <form id="login-form" class="auth-form active">
                    <h2>Welcome Back</h2>
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                    <div id="login-error" class="error-message"></div>
                </form>

                <!-- Signup Form -->
                <form id="signup-form" class="auth-form">
                    <h2>Create Account</h2>
                    <div class="form-group">
                        <label for="signup-email">Email</label>
                        <input type="email" id="signup-email" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-password">Password</label>
                        <input type="password" id="signup-password" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="signup-confirm">Confirm Password</label>
                        <input type="password" id="signup-confirm" required minlength="6">
                    </div>
                    <button type="submit" class="btn btn-primary">Sign Up</button>
                    <div id="signup-error" class="error-message"></div>
                </form>
            </div>
        </section>

        <!-- Portfolio Builder Section -->
        <section id="portfolio-section" class="portfolio-section hidden">
            <div class="portfolio-container">
                <div class="user-info">
                    <span id="user-email"></span>
                    <button id="logout-btn" class="btn btn-secondary">Logout</button>
                </div>

                <h2>Build Your Portfolio</h2>
                
                <form id="portfolio-form" class="portfolio-form">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>Basic Information</h3>
                        <div class="form-group">
                            <label for="portfolio-name">Full Name</label>
                            <input type="text" id="portfolio-name" required>
                        </div>
                        <div class="form-group">
                            <label for="portfolio-username">Username (Optional)</label>
                            <input type="text" id="portfolio-username" placeholder="your-unique-username">
                            <small>This will be used for your public portfolio URL</small>
                        </div>
                        <div class="form-group">
                            <label for="portfolio-bio">Bio</label>
                            <textarea id="portfolio-bio" rows="4" required placeholder="Tell us about yourself..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="portfolio-skills">Skills (comma separated)</label>
                            <input type="text" id="portfolio-skills" required placeholder="JavaScript, React, Node.js, Python">
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="form-section">
                        <h3>Social Links</h3>
                        <div class="form-group">
                            <label for="portfolio-github">GitHub URL</label>
                            <input type="url" id="portfolio-github" placeholder="https://github.com/yourusername">
                        </div>
                        <div class="form-group">
                            <label for="portfolio-linkedin">LinkedIn URL</label>
                            <input type="url" id="portfolio-linkedin" placeholder="https://linkedin.com/in/yourusername">
                        </div>
                    </div>

                    <!-- Projects Section -->
                    <div class="form-section">
                        <h3>Projects</h3>
                        <div id="projects-container">
                            <div class="project-item">
                                <div class="form-group">
                                    <label>Project Title</label>
                                    <input type="text" class="project-title" required>
                                </div>
                                <div class="form-group">
                                    <label>Project Description</label>
                                    <textarea class="project-description" rows="3" required></textarea>
                                </div>
                                <button type="button" class="btn btn-danger remove-project">Remove</button>
                            </div>
                        </div>
                        <button type="button" id="add-project" class="btn btn-secondary">Add Another Project</button>
                    </div>

                    <button type="submit" class="btn btn-primary btn-large">Create Portfolio</button>
                </form>
            </div>
        </section>

        <!-- Portfolio Preview Section -->
        <section id="preview-section" class="preview-section hidden">
            <div class="preview-container">
                <h2>Your Portfolio Preview</h2>
                <div id="portfolio-preview" class="portfolio-preview">
                    <!-- Portfolio content will be dynamically generated here -->
                </div>
                <div class="preview-actions">
                    <button id="edit-portfolio" class="btn btn-secondary">Edit Portfolio</button>
                    <button id="share-portfolio" class="btn btn-primary">Share Portfolio</button>
                </div>
            </div>
        </section>

        <!-- Loading Spinner -->
        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>

        <!-- Success Message -->
        <div id="success-message" class="success-message hidden">
            <p>Portfolio created successfully! Check your email for confirmation.</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
