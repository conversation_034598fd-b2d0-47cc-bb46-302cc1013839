<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PortfolioBuilder - Create Your Professional Portfolio</title>
    <meta name="description" content="Create stunning professional portfolios in minutes. Showcase your skills, projects, and achievements with our modern, responsive platform.">
    <meta name="keywords" content="portfolio, professional, resume, CV, showcase, projects, skills">
    <meta name="author" content="Portfolio Builder">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💼</text></svg>">

    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <script src="config.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-rocket"></i>
                    <span>PortfolioBuilder</span>
                </div>
                <nav class="nav-menu">
                    <a href="#features" class="nav-link">Features</a>
                    <a href="#how-it-works" class="nav-link">How it Works</a>
                    <a href="#pricing" class="nav-link">Pricing</a>
                </nav>
                <div class="header-actions">
                    <button id="login-btn" class="btn btn-ghost">Sign In</button>
                    <button id="signup-btn" class="btn btn-primary">Get Started</button>
                </div>
                <div class="user-menu hidden" id="user-menu">
                    <span class="user-email" id="user-email"></span>
                    <button id="logout-btn" class="btn btn-ghost">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="hero-section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Create Your <span class="highlight">Professional Portfolio</span> in Minutes
                    </h1>
                    <p class="hero-subtitle">
                        Build stunning portfolios that showcase your skills and projects.
                        Stand out from the crowd with our modern, responsive templates.
                    </p>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">10K+</span>
                            <span class="stat-label">Portfolios Created</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">95%</span>
                            <span class="stat-label">Success Rate</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">5 min</span>
                            <span class="stat-label">Average Setup</span>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-large" id="get-started-main">
                            <i class="fas fa-rocket"></i>
                            Start Building Now
                        </button>
                        <button class="btn btn-outline btn-large" id="view-demo">
                            <i class="fas fa-play"></i>
                            View Demo
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="testSupabase()" style="margin-top: 1rem;">
                            <i class="fas fa-database"></i>
                            Test Connection
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="testAuth()" style="margin-top: 0.5rem;">
                            <i class="fas fa-user-check"></i>
                            Test Auth
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="testLogin()" style="margin-top: 0.5rem;">
                            <i class="fas fa-sign-in-alt"></i>
                            Test Login Form
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="directLoginTest()" style="margin-top: 0.5rem;">
                            <i class="fas fa-bolt"></i>
                            Direct Login Test
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="debugForms()" style="margin-top: 0.5rem;">
                            <i class="fas fa-bug"></i>
                            Debug Forms
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="manualLogin()" style="margin-top: 0.5rem;">
                            <i class="fas fa-wrench"></i>
                            Manual Login
                        </button>
                        <button class="btn btn-ghost btn-large" onclick="quickSignup()" style="margin-top: 0.5rem;">
                            <i class="fas fa-user-plus"></i>
                            Quick Signup
                        </button>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="portfolio-preview">
                        <div class="preview-window">
                            <div class="window-header">
                                <div class="window-controls">
                                    <span class="control red"></span>
                                    <span class="control yellow"></span>
                                    <span class="control green"></span>
                                </div>
                                <div class="window-title">yourportfolio.com</div>
                            </div>
                            <div class="window-content">
                                <div class="demo-profile">
                                    <div class="demo-avatar"></div>
                                    <div class="demo-info">
                                        <div class="demo-name"></div>
                                        <div class="demo-role"></div>
                                    </div>
                                </div>
                                <div class="demo-skills">
                                    <div class="demo-skill"></div>
                                    <div class="demo-skill"></div>
                                    <div class="demo-skill"></div>
                                    <div class="demo-skill"></div>
                                </div>
                                <div class="demo-projects">
                                    <div class="demo-project"></div>
                                    <div class="demo-project"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="modal hidden">
        <div class="modal-overlay" id="auth-overlay"></div>
        <div class="modal-container">
            <div class="auth-card">
                <button class="modal-close" id="auth-close">
                    <i class="fas fa-times"></i>
                </button>

                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-rocket"></i>
                        <span>PortfolioBuilder</span>
                    </div>
                </div>

                <div class="auth-tabs">
                    <button id="login-tab" class="tab-btn active" data-tab="login">
                        Sign In
                    </button>
                    <button id="signup-tab" class="tab-btn" data-tab="signup">
                        Sign Up
                    </button>
                </div>

                <!-- Login Form -->
                <div id="login-form-container" class="tab-content active">
                    <div class="form-header">
                        <h2>Welcome back</h2>
                        <p>Sign in to your account to continue</p>
                    </div>

                    <form id="login-form" class="auth-form">
                        <div class="input-field">
                            <label for="login-email">Email address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="login-email" placeholder="<EMAIL>" required>
                            </div>
                        </div>

                        <div class="input-field">
                            <label for="login-password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="login-password" placeholder="Enter your password" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-full">
                            Sign In
                        </button>

                        <div id="login-error" class="error-message"></div>
                    </form>
                </div>

                <!-- Signup Form -->
                <div id="signup-form-container" class="tab-content">
                    <div class="form-header">
                        <h2>Create account</h2>
                        <p>Start building your professional portfolio today</p>
                    </div>

                    <form id="signup-form" class="auth-form">
                        <div class="input-field">
                            <label for="signup-email">Email address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="signup-email" placeholder="<EMAIL>" required>
                            </div>
                        </div>

                        <div class="input-field">
                            <label for="signup-password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="signup-password" placeholder="Create a password" required minlength="6">
                            </div>
                        </div>

                        <div class="input-field">
                            <label for="signup-confirm">Confirm password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="signup-confirm" placeholder="Confirm your password" required minlength="6">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-full">
                            Create Account
                        </button>

                        <div id="signup-error" class="error-message"></div>

                        <div class="auth-footer">
                            <p>By creating an account, you agree to our <a href="#">Terms</a> and <a href="#">Privacy Policy</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Portfolio Builder Section -->
    <section id="portfolio-section" class="portfolio-section hidden">
        <div class="container">
            <div class="portfolio-header">
                <h1>Build Your Portfolio</h1>
                <p>Create a professional portfolio that showcases your skills and projects</p>
            </div>

            <div class="portfolio-form-container">
                <form id="portfolio-form" class="portfolio-form">
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Basic Information</h2>
                            <p>Tell us about yourself and your professional background</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="portfolio-name">Full Name *</label>
                                <input type="text" id="portfolio-name" placeholder="John Doe" required>
                            </div>

                            <div class="form-group">
                                <label for="portfolio-username">Username</label>
                                <input type="text" id="portfolio-username" placeholder="johndoe">
                                <small>Optional - Used for your portfolio URL</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="portfolio-bio">Professional Bio *</label>
                            <textarea id="portfolio-bio" rows="4" required placeholder="Tell us about your professional journey, expertise, and what makes you unique..."></textarea>
                            <div class="char-counter">
                                <span id="bio-counter">0</span>/500 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="portfolio-skills">Skills & Technologies *</label>
                            <input type="text" id="portfolio-skills" required placeholder="JavaScript, React, Node.js, Python, AWS">
                            <small>Separate skills with commas</small>
                            <div class="skills-preview" id="skills-preview"></div>
                        </div>
                    </div>

                    <!-- Social Links Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Social Links</h2>
                            <p>Connect your professional profiles</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="portfolio-github">
                                    <i class="fab fa-github"></i>
                                    GitHub Profile
                                </label>
                                <input type="url" id="portfolio-github" placeholder="https://github.com/yourusername">
                            </div>

                            <div class="form-group">
                                <label for="portfolio-linkedin">
                                    <i class="fab fa-linkedin"></i>
                                    LinkedIn Profile
                                </label>
                                <input type="url" id="portfolio-linkedin" placeholder="https://linkedin.com/in/yourusername">
                            </div>
                        </div>
                    </div>

                    <!-- Projects Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>Projects</h2>
                            <p>Showcase your best work and achievements</p>
                        </div>

                        <div id="projects-container">
                            <div class="project-item">
                                <div class="project-header">
                                    <h3>Project #1</h3>
                                    <button type="button" class="btn-remove" title="Remove Project">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="form-group">
                                    <label>Project Title *</label>
                                    <input type="text" class="project-title" placeholder="My Awesome Project" required>
                                </div>

                                <div class="form-group">
                                    <label>Project Description *</label>
                                    <textarea class="project-description" rows="3" placeholder="Describe your project, technologies used, and key achievements..." required></textarea>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="add-project" class="btn btn-outline">
                            <i class="fas fa-plus"></i>
                            Add Another Project
                        </button>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" id="save-draft" class="btn btn-outline">
                            <i class="fas fa-save"></i>
                            Save Draft
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Create Portfolio
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Portfolio Preview Section -->
    <section id="preview-section" class="preview-section hidden">
        <div class="preview-container">
            <div class="preview-header">
                <div class="preview-title">
                    <h2>Your Portfolio is Ready! 🎉</h2>
                    <p>Here's how your portfolio looks to visitors</p>
                </div>
                <div class="preview-actions">
                    <button id="edit-portfolio" class="btn btn-outline">
                        <i class="fas fa-edit"></i>
                        Edit Portfolio
                    </button>
                    <button id="share-portfolio" class="btn btn-primary">
                        <i class="fas fa-share-alt"></i>
                        Share Portfolio
                    </button>
                </div>
            </div>

            <div class="preview-wrapper">
                <div class="preview-device">
                    <div class="device-header">
                        <div class="device-buttons">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div class="device-url">
                            <i class="fas fa-lock"></i>
                            <span id="portfolio-url">portfoliobuilder.com/your-portfolio</span>
                        </div>
                    </div>
                    <div id="portfolio-preview" class="portfolio-preview">
                        <!-- Portfolio content will be dynamically generated here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div id="loading" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <h3>Creating your portfolio...</h3>
            <p id="loading-text">Setting up your professional showcase</p>
        </div>
    </div>

    <!-- Success Notification -->
    <div id="success-message" class="notification success hidden">
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <div class="notification-text">
                <h4>Portfolio Created Successfully!</h4>
                <p>Check your email for confirmation details</p>
            </div>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Share Modal -->
    <div id="share-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Share Your Portfolio</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="share-url">
                    <label>Your Portfolio URL:</label>
                    <div class="url-input">
                        <input type="text" id="share-url-input" readonly>
                        <button id="copy-url" class="btn btn-primary">
                            <i class="fas fa-copy"></i>
                            Copy
                        </button>
                    </div>
                </div>
                <div class="share-social">
                    <h4>Share on Social Media:</h4>
                    <div class="social-buttons">
                        <button class="social-btn twitter" id="share-twitter">
                            <i class="fab fa-twitter"></i>
                            Twitter
                        </button>
                        <button class="social-btn linkedin" id="share-linkedin">
                            <i class="fab fa-linkedin"></i>
                            LinkedIn
                        </button>
                        <button class="social-btn facebook" id="share-facebook">
                            <i class="fab fa-facebook"></i>
                            Facebook
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
