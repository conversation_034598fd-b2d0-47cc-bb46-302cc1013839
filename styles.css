/* Modern Portfolio Builder - Clean Design */

/* CSS Variables for consistent theming */
:root {
    /* Primary Colors - Clean Blue */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;

    /* Main Theme Colors */
    --primary: var(--primary-600);
    --primary-hover: var(--primary-700);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-400);
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --border: var(--gray-200);
    --border-light: var(--gray-100);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 600; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 500; }

p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Header */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(8px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary);
}

.logo i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover {
    color: var(--primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-email {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Utility Classes */
.hidden { display: none !important; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.highlight {
    color: var(--primary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 1px solid transparent;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}



.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-full {
    width: 100%;
}

.btn-remove {
    background: transparent;
    border: none;
    color: var(--text-muted);
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s;
}

.btn-remove:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

/* Additional button improvements */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Improve button text contrast */
.btn-primary {
    background: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
    font-weight: 600;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-700);
    border-color: var(--primary-700);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
    font-weight: 500;
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border-color: transparent;
    font-weight: 500;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--text-primary);
    border-color: var(--gray-200);
    transform: translateY(-1px);
}

/* Hero Section */
.hero {
    padding: 6rem 0 4rem;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--bg-primary) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Portfolio Preview */
.hero-visual {
    display: flex;
    justify-content: center;
}

.portfolio-preview {
    position: relative;
}

.preview-window {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    width: 400px;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.preview-window:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.window-header {
    background: var(--gray-100);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--border);
}

.window-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28ca42; }

.window-title {
    font-size: 0.875rem;
    color: var(--text-muted);
    background: var(--bg-primary);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border);
}

.window-content {
    padding: 2rem;
}

.demo-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.demo-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--primary-700));
}

.demo-name {
    height: 20px;
    width: 120px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    margin-bottom: 0.5rem;
}

.demo-role {
    height: 16px;
    width: 80px;
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

.demo-skills {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.demo-skill {
    height: 24px;
    width: 60px;
    background: var(--primary-100);
    border-radius: var(--radius-lg);
}

.demo-projects {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.demo-project {
    height: 80px;
    background: var(--gray-50);
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

/* Ensure modal is visible when not hidden */
.modal:not(.hidden) {
    display: flex !important;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-container {
    position: relative;
    z-index: 10;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius);
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s;
    z-index: 10;
}

.modal-close:hover {
    background: var(--gray-200);
    color: var(--text-primary);
}

/* Authentication Modal */
.auth-card {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 400px;
    padding: 2rem;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.auth-logo i {
    font-size: 2rem;
}

.auth-tabs {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius);
    padding: 0.25rem;
    margin-bottom: 2rem;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
}

.tab-btn.active {
    background: var(--bg-primary);
    color: var(--primary);
    box-shadow: var(--shadow-sm);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-field label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: all 0.2s;
    background: var(--bg-primary);
    color: var(--text-primary);
    z-index: 1;
    position: relative;
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-100);
}

.input-wrapper input::placeholder {
    color: var(--text-muted);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.875rem;
    text-align: center;
    padding: 0.75rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: var(--radius);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
}

.auth-footer p {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.4;
}

.auth-footer a {
    color: var(--primary);
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Portfolio Section */
.portfolio-section {
    padding: 4rem 0;
    background: var(--bg-secondary);
    min-height: 100vh;
}

.portfolio-header {
    text-align: center;
    margin-bottom: 3rem;
}

.portfolio-header h1 {
    margin-bottom: 1rem;
}

.portfolio-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.portfolio-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.portfolio-form {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: 3rem;
}

.form-section {
    margin-bottom: 3rem;
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.section-header h2 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.section-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group label i {
    margin-right: 0.5rem;
    color: var(--primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: all 0.2s;
    background: var(--bg-primary);
    font-family: inherit;
}

.form-group input:hover,
.form-group textarea:hover {
    border-color: var(--primary-300);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-100);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.char-counter {
    text-align: right;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.skills-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.skill-tag {
    background: var(--primary-100);
    color: var(--primary-700);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Project Items */
.project-item {
    background: var(--gray-50);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-header h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border);
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header-content {
        height: 3.5rem;
    }

    .nav-menu {
        display: none;
    }

    .hero {
        padding: 4rem 0 3rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .preview-window {
        width: 300px;
        transform: none;
    }

    .portfolio-form {
        padding: 2rem 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .auth-card {
        margin: 1rem;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-large {
        width: 100%;
    }
}

/* Notifications */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    max-width: 400px;
    z-index: 60;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid var(--primary);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.info {
    border-left-color: var(--primary);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.25rem;
}

.notification.success .notification-content i {
    color: var(--success-color);
}

.notification.error .notification-content i {
    color: var(--danger-color);
}

.notification.info .notification-content i {
    color: var(--primary);
}

.notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 60;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.loading-content p {
    color: rgba(255, 255, 255, 0.8);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-user span {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-white);
}

.btn-danger:hover:not(:disabled) {
    background: #dc2626;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.btn-add {
    border: 2px dashed var(--border-color);
    color: var(--text-secondary);
    background: transparent;
}

.btn-add:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--primary-light);
    background-opacity: 0.1;
}

.btn-icon {
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

/* CTA Button */
.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 6rem 2rem 4rem;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -2;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    color: var(--text-white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.feature-item i {
    color: var(--secondary-color);
    font-size: 1.25rem;
}

/* Portfolio Mockup */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.portfolio-mockup {
    width: 400px;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.portfolio-mockup:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.mockup-header {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.mockup-dots {
    display: flex;
    gap: 0.5rem;
}

.mockup-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-color);
}

.mockup-dots span:nth-child(1) { background: #ff5f57; }
.mockup-dots span:nth-child(2) { background: #ffbd2e; }
.mockup-dots span:nth-child(3) { background: #28ca42; }

.mockup-content {
    padding: 2rem;
}

.mockup-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mockup-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.mockup-name {
    height: 20px;
    width: 120px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    margin-bottom: 0.5rem;
}

.mockup-title {
    height: 16px;
    width: 80px;
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

.mockup-skills {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.mockup-skill {
    height: 24px;
    width: 60px;
    background: var(--primary-light);
    border-radius: var(--radius-lg);
}

.mockup-projects {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mockup-project {
    height: 80px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

/* Authentication Section */
.auth-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.auth-modal {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 450px;
    margin: 2rem;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-header {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1rem 0;
}

.auth-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.auth-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.auth-content {
    padding: 0 2rem 2rem;
}

.auth-brand {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-brand i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.auth-brand h2 {
    color: var(--text-primary);
    font-weight: 700;
}

.auth-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 0.25rem;
    margin-bottom: 2rem;
}

.auth-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.auth-tab.active {
    background: var(--bg-primary);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-group input::placeholder {
    color: var(--text-muted);
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 1rem;
    text-align: center;
    padding: 0.75rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: var(--radius-md);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-footer p {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Portfolio Section */
.portfolio-section {
    min-height: 100vh;
    padding: 6rem 2rem 4rem;
    background: var(--bg-secondary);
}

.portfolio-container {
    max-width: 800px;
    margin: 0 auto;
}

.portfolio-header {
    text-align: center;
    margin-bottom: 3rem;
}

.portfolio-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-muted);
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
}

.progress-step span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
}

.progress-step.active span {
    color: var(--text-primary);
}

.progress-line {
    width: 60px;
    height: 2px;
    background: var(--border-color);
    margin: 0 1rem;
}

.portfolio-title h2 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.portfolio-title p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Form Steps */
.portfolio-form {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.form-step {
    display: none;
    padding: 3rem;
}

.form-step.active {
    display: block;
    animation: stepSlideIn 0.3s ease;
}

@keyframes stepSlideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.step-header i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.step-header h3 {
    font-size: 1.75rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.step-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    font-family: inherit;
    position: relative;
}

.form-group input:hover,
.form-group textarea:hover {
    border-color: var(--primary-light);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.char-counter {
    text-align: right;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.skills-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.skill-tag {
    background: var(--primary-light);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Project Items */
.project-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-header h4 {
    color: var(--text-primary);
    font-weight: 600;
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 3rem;
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
}

.form-navigation .btn {
    min-width: 120px;
}

/* Preview Section */
.preview-section {
    min-height: 100vh;
    padding: 6rem 2rem 4rem;
    background: var(--bg-secondary);
}

.preview-container {
    max-width: 1200px;
    margin: 0 auto;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 2rem;
}

.preview-title h2 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.preview-title p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

.preview-actions {
    display: flex;
    gap: 1rem;
}

.preview-wrapper {
    display: flex;
    justify-content: center;
}

.preview-device {
    width: 100%;
    max-width: 900px;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.device-header {
    background: var(--bg-tertiary);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.device-buttons {
    display: flex;
    gap: 0.5rem;
}

.device-buttons span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.device-buttons span:nth-child(1) { background: #ff5f57; }
.device-buttons span:nth-child(2) { background: #ffbd2e; }
.device-buttons span:nth-child(3) { background: #28ca42; }

.device-url {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--bg-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.device-url i {
    color: var(--success-color);
}

/* Portfolio Preview Content */
.portfolio-preview {
    padding: 3rem;
    background: var(--bg-primary);
}

.preview-header-content {
    text-align: center;
    margin-bottom: 3rem;
}

.preview-name {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.preview-bio {
    font-size: 1.25rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.skill-badge {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    cursor: default;
}

.skill-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-secondary);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.projects-section h3 {
    text-align: center;
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.project-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-content {
    text-align: center;
    color: var(--text-white);
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: 0.1s;
    border-top-color: var(--secondary-color);
}

.spinner-ring:nth-child(3) {
    animation-delay: 0.2s;
    border-top-color: var(--success-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.loading-content p {
    color: rgba(255, 255, 255, 0.8);
}

/* Notifications */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1.5rem;
    max-width: 400px;
    z-index: 4000;
    animation: notificationSlideIn 0.3s ease;
    border-left: 4px solid var(--success-color);
}

.notification.success {
    border-left-color: var(--success-color);
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.notification-content i {
    color: var(--success-color);
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.notification-text h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.notification-text p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.notification-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.share-url {
    margin-bottom: 2rem;
}

.share-url label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.url-input {
    display: flex;
    gap: 0.5rem;
}

.url-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    font-size: 0.875rem;
}

.share-social h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.social-buttons {
    display: flex;
    gap: 1rem;
}

.social-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.social-btn.twitter {
    background: #1da1f2;
    color: white;
}

.social-btn.linkedin {
    background: #0077b5;
    color: white;
}

.social-btn.facebook {
    background: #1877f2;
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .portfolio-mockup {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 0.75rem 0;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-brand span {
        display: none;
    }

    .hero {
        padding: 5rem 1rem 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .portfolio-mockup {
        width: 300px;
    }

    .auth-modal {
        margin: 1rem;
    }

    .auth-content {
        padding: 0 1.5rem 1.5rem;
    }

    .portfolio-section,
    .preview-section {
        padding: 5rem 1rem 3rem;
    }

    .form-step {
        padding: 2rem 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-navigation {
        padding: 1.5rem;
        flex-direction: column;
        gap: 1rem;
    }

    .form-navigation .btn {
        width: 100%;
    }

    .preview-header {
        flex-direction: column;
        text-align: center;
    }

    .preview-title h2 {
        font-size: 2rem;
    }

    .preview-name {
        font-size: 2.5rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .social-links {
        flex-direction: column;
        align-items: center;
    }

    .notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .modal {
        padding: 1rem;
    }

    .modal-header,
    .modal-body {
        padding: 1.5rem;
    }

    .social-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .portfolio-title h2 {
        font-size: 2rem;
    }

    .preview-title h2 {
        font-size: 1.75rem;
    }

    .preview-name {
        font-size: 2rem;
    }

    .progress-step span {
        display: none;
    }

    .progress-line {
        width: 40px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .auth-section,
    .loading-overlay,
    .notification,
    .modal {
        display: none !important;
    }

    .preview-section {
        padding: 0;
    }

    .preview-device {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

/* Authentication Section */
.auth-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
}

.auth-container {
    padding: 40px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 2px solid #f0f0f0;
}

.tab-button {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    color: #666;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #4CAF50;
    border-bottom: 3px solid #4CAF50;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
}

.form-group small {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

.error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 10px;
    text-align: center;
}

/* Portfolio Section */
.portfolio-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.portfolio-container {
    padding: 40px;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.user-info span {
    font-weight: 600;
    color: #666;
}

.portfolio-form h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.form-section {
    margin-bottom: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 12px;
}

.form-section h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
}

/* Projects Section */
.project-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 2px solid #e0e0e0;
    position: relative;
}

.project-item .remove-project {
    position: absolute;
    top: 10px;
    right: 10px;
}

#add-project {
    width: 100%;
    margin-top: 10px;
}

/* Preview Section */
.preview-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.preview-container {
    padding: 40px;
}

.preview-container h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.portfolio-preview {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.preview-header {
    text-align: center;
    margin-bottom: 30px;
}

.preview-name {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 10px;
}

.preview-bio {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 30px;
}

.skill-badge {
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.social-link {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 600;
    padding: 10px 20px;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #4CAF50;
    color: white;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.project-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4CAF50;
}

.project-title {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.project-description {
    color: #666;
    line-height: 1.6;
}

.preview-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Loading Spinner */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    margin-top: 20px;
    font-size: 18px;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .auth-container,
    .portfolio-container,
    .preview-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 20px;
    }
    
    .preview-name {
        font-size: 2rem;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .preview-actions {
        flex-direction: column;
    }
    
    .social-links {
        flex-direction: column;
        align-items: center;
    }
}
