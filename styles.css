/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

/* Authentication Section */
.auth-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
}

.auth-container {
    padding: 40px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 2px solid #f0f0f0;
}

.tab-button {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    color: #666;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #4CAF50;
    border-bottom: 3px solid #4CAF50;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
}

.form-group small {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

.error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 10px;
    text-align: center;
}

/* Portfolio Section */
.portfolio-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.portfolio-container {
    padding: 40px;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.user-info span {
    font-weight: 600;
    color: #666;
}

.portfolio-form h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.form-section {
    margin-bottom: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 12px;
}

.form-section h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
}

/* Projects Section */
.project-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 2px solid #e0e0e0;
    position: relative;
}

.project-item .remove-project {
    position: absolute;
    top: 10px;
    right: 10px;
}

#add-project {
    width: 100%;
    margin-top: 10px;
}

/* Preview Section */
.preview-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.preview-container {
    padding: 40px;
}

.preview-container h2 {
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.portfolio-preview {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.preview-header {
    text-align: center;
    margin-bottom: 30px;
}

.preview-name {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 10px;
}

.preview-bio {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 30px;
}

.skill-badge {
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.social-link {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 600;
    padding: 10px 20px;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #4CAF50;
    color: white;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.project-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4CAF50;
}

.project-title {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.project-description {
    color: #666;
    line-height: 1.6;
}

.preview-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Loading Spinner */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    margin-top: 20px;
    font-size: 18px;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .auth-container,
    .portfolio-container,
    .preview-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 20px;
    }
    
    .preview-name {
        font-size: 2rem;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .preview-actions {
        flex-direction: column;
    }
    
    .social-links {
        flex-direction: column;
        align-items: center;
    }
}
