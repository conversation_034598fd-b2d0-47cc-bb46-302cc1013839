// Demo Configuration for Portfolio Builder App
// This is a sample configuration file showing the structure
// Copy this to config.js and replace with your actual credentials

const CONFIG = {
    SUPABASE: {
        // Replace with your Supabase project URL
        // Found in: Supabase Dashboard > Settings > API > Project URL
        URL: 'https://your-project-ref.supabase.co',
        
        // Replace with your Supabase anon/public key
        // Found in: Supabase Dashboard > Settings > API > Project API keys > anon public
        ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key-here'
    },
    
    EMAILJS: {
        // Replace with your EmailJS service ID
        // Found in: EmailJS Dashboard > Email Services > Service ID
        SERVICE_ID: 'service_your_id',
        
        // Replace with your EmailJS template ID
        // Found in: EmailJS Dashboard > Email Templates > Template ID
        TEMPLATE_ID: 'template_your_id',
        
        // Replace with your EmailJS user ID (public key)
        // Found in: EmailJS Dashboard > Account > General > Public Key
        USER_ID: 'your_user_id'
    }
};

// Export configuration for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}

/* 
SETUP INSTRUCTIONS:

1. SUPABASE SETUP:
   - Go to https://supabase.com and create a new project
   - Copy the Project URL and anon key from Settings > API
   - Run the SQL commands from README.md to create the portfolios table
   
2. EMAILJS SETUP:
   - Go to https://www.emailjs.com and create an account
   - Create an email service (Gmail, Outlook, etc.)
   - Create an email template using the provided email-template.html
   - Copy the Service ID, Template ID, and User ID
   
3. CONFIGURATION:
   - Copy this file to config.js
   - Replace all placeholder values with your actual credentials
   - Save the file and refresh the application

4. TESTING:
   - Open the application in your browser
   - Try creating an account and building a portfolio
   - Check that emails are sent and data is saved to Supabase
*/
