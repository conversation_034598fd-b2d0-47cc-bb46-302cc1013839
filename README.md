# Portfolio Builder App

A single-page portfolio builder web application built with HTML, CSS, and JavaScript. Users can create professional portfolios with authentication, database storage, and email notifications.

## Features

- 🔐 **User Authentication** - Secure login/signup with Supabase Auth
- 📝 **Portfolio Builder** - Dynamic form to create professional portfolios
- 🎨 **Live Preview** - Real-time portfolio preview with modern design
- 💾 **Database Storage** - Portfolio data saved to Supabase database
- 📧 **Email Notifications** - Confirmation emails via EmailJS
- 🔗 **Public Sharing** - Shareable portfolio URLs
- 📱 **Responsive Design** - Works on all devices

## Setup Instructions

### 1. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Create the portfolios table by running this SQL in the SQL Editor:

```sql
-- Create portfolios table
CREATE TABLE portfolios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    username TEXT UNIQUE,
    bio TEXT NOT NULL,
    skills TEXT[] NOT NULL,
    github_url TEXT,
    linkedin_url TEXT,
    projects JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can insert their own portfolio" ON portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own portfolio" ON portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own portfolio" ON portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view portfolios for public sharing" ON portfolios
    FOR SELECT USING (true);

-- Create index for username lookups
CREATE INDEX idx_portfolios_username ON portfolios(username);
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
```

4. Update the configuration in `script.js`:
```javascript
const SUPABASE_URL = 'your-project-url';
const SUPABASE_ANON_KEY = 'your-anon-key';
```

### 2. EmailJS Setup

1. Create an account at [emailjs.com](https://www.emailjs.com/)
2. Create a new email service (Gmail, Outlook, etc.)
3. Create an email template with these variables:
   - `{{to_email}}` - Recipient email
   - `{{user_name}}` - User's name
   - `{{portfolio_url}}` - Portfolio URL
   - `{{skills_count}}` - Number of skills
   - `{{projects_count}}` - Number of projects

4. Update the configuration in `script.js`:
```javascript
const EMAILJS_SERVICE_ID = 'your-service-id';
const EMAILJS_TEMPLATE_ID = 'your-template-id';
const EMAILJS_USER_ID = 'your-user-id';
```

### 3. Running the Application

1. Open `index.html` in a web browser
2. Or serve it using a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## Usage

1. **Sign Up/Login** - Create an account or log in with existing credentials
2. **Build Portfolio** - Fill out the form with your information:
   - Basic info (name, bio, skills)
   - Social links (GitHub, LinkedIn)
   - Projects (title and description)
   - Optional username for custom URL
3. **Preview** - View your portfolio in real-time
4. **Share** - Get a shareable URL for your portfolio

## File Structure

```
portfolio-builder/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Supabase (PostgreSQL database + Auth)
- **Email**: EmailJS
- **Hosting**: Can be deployed to any static hosting service

## Deployment

This app can be deployed to any static hosting service:

- **Netlify**: Drag and drop the folder
- **Vercel**: Connect your GitHub repository
- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Firebase Hosting**: Use Firebase CLI

## Security Features

- Row Level Security (RLS) enabled on database
- User authentication required for portfolio creation
- Public portfolios are read-only
- Input validation and sanitization

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

MIT License - feel free to use this project for personal or commercial purposes.
