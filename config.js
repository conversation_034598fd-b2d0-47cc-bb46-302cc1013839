// Configuration file for Portfolio Builder App
// Replace the placeholder values with your actual credentials

// Supabase Configuration
// Get these from your Supabase project dashboard: Settings > API
const CONFIG = {
    SUPABASE: {
        URL: 'YOUR_SUPABASE_URL', // e.g., 'https://your-project.supabase.co'
        ANON_KEY: 'YOUR_SUPABASE_ANON_KEY' // Your public anon key
    },
    
    // EmailJS Configuration
    // Get these from your EmailJS dashboard
    EMAILJS: {
        SERVICE_ID: 'YOUR_EMAILJS_SERVICE_ID', // e.g., 'service_abc123'
        TEMPLATE_ID: 'YOUR_EMAILJS_TEMPLATE_ID', // e.g., 'template_xyz789'
        USER_ID: 'YOUR_EMAILJS_USER_ID' // e.g., 'user_def456'
    }
};

// Export configuration for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
