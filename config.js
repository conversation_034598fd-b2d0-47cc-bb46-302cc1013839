// Configuration file for Portfolio Builder App
// Supabase credentials configured for project: lwmqufwuzmmruecyaxgg

// Supabase Configuration - Direct variables for easy access
const SUPABASE_URL = 'https://lwmqufwuzmmruecyaxgg.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bXF1Znd1em1tcnVlY3lheGdnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MTMzODIsImV4cCI6MjA2Nzk4OTM4Mn0.H2YHgnBv7lWNqDfxfhKojHsAN6Fy2Yrlj6ul94Jvj_Q';

// EmailJS Configuration
const EMAILJS_SERVICE_ID = 'service_og4n5il';
const EMAILJS_TEMPLATE_ID = 'company123';
const EMAILJS_USER_ID = '1MrS2synlODS4l0OB';

// Legacy CONFIG object for backward compatibility
const CONFIG = {
    SUPABASE: {
        URL: SUPABASE_URL,
        ANON_KEY: SUPABASE_ANON_KEY
    },
    EMAILJS: {
        SERVICE_ID: EMAILJS_SERVICE_ID,
        TEMPLATE_ID: EMAILJS_TEMPLATE_ID,
        USER_ID: EMAILJS_USER_ID
    }
};

// Export configuration for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
