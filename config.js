// Configuration file for Portfolio Builder App
// Supabase credentials configured for project: lwmqufwuzmmruecyaxgg

// Supabase Configuration
const CONFIG = {
    SUPABASE: {
        URL: 'https://lwmqufwuzmmruecyaxgg.supabase.co',
        ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bXF1Znd1em1tcnVlY3lheGdnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MTMzODIsImV4cCI6MjA2Nzk4OTM4Mn0.H2YHgnBv7lWNqDfxfhKojHsAN6Fy2Yrlj6ul94Jvj_Q'
    },

    // EmailJS Configuration
    // Update these when you set up EmailJS (optional for basic functionality)
    EMAILJS: {
        SERVICE_ID: 'YOUR_EMAILJS_SERVICE_ID', // e.g., 'service_abc123'
        TEMPLATE_ID: 'YOUR_EMAILJS_TEMPLATE_ID', // e.g., 'template_xyz789'
        USER_ID: 'YOUR_EMAILJS_USER_ID' // e.g., 'user_def456'
    }
};

// Export configuration for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
