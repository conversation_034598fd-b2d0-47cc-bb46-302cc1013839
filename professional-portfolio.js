// Global variables
let supabase;
let currentUser = null;

// DOM Elements
const authButtons = document.getElementById('auth-buttons');
const userMenu = document.getElementById('user-menu');
const userEmail = document.getElementById('user-email');
const dashboardEmail = document.getElementById('dashboard-email');
const userAvatar = document.getElementById('user-avatar');
const heroSection = document.getElementById('hero-section');
const dashboardSection = document.getElementById('dashboard-section');
const authModal = document.getElementById('auth-modal');
const loading = document.getElementById('loading');

// Initialize Supabase
function initSupabase() {
    console.log('🔧 Initializing Supabase...');
    
    if (!SUPABASE_CONFIG.URL || !SUPABASE_CONFIG.ANON_KEY) {
        console.error('❌ Supabase configuration missing');
        return false;
    }
    
    try {
        supabase = window.supabase.createClient(SUPABASE_CONFIG.URL, SUPABASE_CONFIG.ANON_KEY);
        console.log('✅ Supabase initialized successfully');
        console.log('📍 URL:', SUPABASE_CONFIG.URL);
        return true;
    } catch (error) {
        console.error('❌ Supabase initialization failed:', error);
        return false;
    }
}

// Utility Functions
function showLoading(show = true) {
    if (show) {
        loading.classList.add('show');
    } else {
        loading.classList.remove('show');
    }
}

function showMessage(elementId, message, type = 'error') {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.classList.remove('show');
        // Force reflow
        element.offsetHeight;
        element.classList.add('show');
        
        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                element.classList.remove('show');
            }, 5000);
        }
    }
}

function hideMessage(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove('show');
        element.textContent = '';
    }
}

function hideAllMessages() {
    hideMessage('login-error');
    hideMessage('login-success');
    hideMessage('signup-error');
    hideMessage('signup-success');
}

// Modal Functions
function showModal() {
    authModal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Add smooth entrance animation
    setTimeout(() => {
        authModal.querySelector('.modal-content').style.transform = 'scale(1)';
    }, 10);
}

function hideModal() {
    authModal.classList.remove('show');
    document.body.style.overflow = '';
    
    // Clear all forms and messages
    document.getElementById('login-form').reset();
    document.getElementById('signup-form').reset();
    hideAllMessages();
}

function switchTab(tabName) {
    console.log('🔄 Switching to tab:', tabName);
    
    // Update tab buttons with smooth transition
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });
    
    // Update tab content with fade animation
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    const activeTab = document.getElementById(tabName + '-tab');
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Update modal header
    const modalTitle = document.getElementById('modal-title');
    const modalSubtitle = document.getElementById('modal-subtitle');
    
    if (tabName === 'login') {
        modalTitle.textContent = 'Welcome back';
        modalSubtitle.textContent = 'Sign in to your account';
    } else {
        modalTitle.textContent = 'Create account';
        modalSubtitle.textContent = 'Start building your portfolio today';
    }
    
    // Clear messages when switching tabs
    hideAllMessages();
}

// UI State Management
function showAuthenticatedState(user) {
    console.log('🟢 User authenticated:', user.email);
    
    // Hide auth buttons, show user menu
    authButtons.classList.add('hidden');
    userMenu.classList.remove('hidden');
    
    // Update email displays
    userEmail.textContent = user.email;
    dashboardEmail.textContent = user.email;
    
    // Update user avatar with first letter of email
    const firstLetter = user.email.charAt(0).toUpperCase();
    userAvatar.innerHTML = firstLetter;
    
    // Show dashboard, hide hero with smooth transition
    heroSection.style.display = 'none';
    dashboardSection.classList.add('show');
    
    currentUser = user;
    
    // Smooth scroll to dashboard
    dashboardSection.scrollIntoView({ behavior: 'smooth' });
}

function showUnauthenticatedState() {
    console.log('🔴 User not authenticated');
    
    // Show auth buttons, hide user menu
    authButtons.classList.remove('hidden');
    userMenu.classList.add('hidden');
    
    // Show hero, hide dashboard
    heroSection.style.display = 'block';
    dashboardSection.classList.remove('show');
    
    currentUser = null;
}

// Authentication Functions
async function handleLogin(e) {
    e.preventDefault();
    console.log('🔐 Login form submitted');
    
    const email = document.getElementById('login-email').value.trim();
    const password = document.getElementById('login-password').value;
    
    console.log('📧 Login attempt with email:', email);
    
    // Clear previous messages
    hideAllMessages();
    
    // Validation
    if (!email || !password) {
        showMessage('login-error', 'Please fill in all fields');
        return;
    }
    
    if (!supabase) {
        showMessage('login-error', 'Authentication service not available. Please refresh the page.');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            console.error('❌ Login error:', error.message);
            showMessage('login-error', error.message);
            return;
        }
        
        console.log('🟢 Login success');
        console.log('User data:', data.user);
        
        showMessage('login-success', '🎉 Welcome back! Redirecting to your dashboard...', 'success');
        
        // Wait a moment then hide modal and show authenticated state
        setTimeout(() => {
            hideModal();
            showAuthenticatedState(data.user);
        }, 2000);
        
    } catch (error) {
        console.error('❌ Login exception:', error);
        showMessage('login-error', 'An unexpected error occurred. Please try again.');
    } finally {
        showLoading(false);
    }
}

async function handleSignup(e) {
    e.preventDefault();
    console.log('📝 Signup form submitted');
    
    const email = document.getElementById('signup-email').value.trim();
    const password = document.getElementById('signup-password').value;
    const confirmPassword = document.getElementById('signup-confirm').value;
    
    console.log('📧 Signup attempt with email:', email);
    
    // Clear previous messages
    hideAllMessages();
    
    // Validation
    if (!email || !password || !confirmPassword) {
        showMessage('signup-error', 'Please fill in all fields');
        return;
    }
    
    if (password !== confirmPassword) {
        showMessage('signup-error', 'Passwords do not match');
        return;
    }
    
    if (password.length < 6) {
        showMessage('signup-error', 'Password must be at least 6 characters');
        return;
    }
    
    if (!supabase) {
        showMessage('signup-error', 'Authentication service not available. Please refresh the page.');
        return;
    }
    
    showLoading(true);
    
    try {
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password,
            options: {
                emailRedirectTo: window.location.origin
            }
        });
        
        if (error) {
            console.error('❌ Signup error:', error.message);
            showMessage('signup-error', error.message);
            return;
        }
        
        console.log('🟢 Signup success');
        console.log('User data:', data.user);
        
        // Check if email confirmation is required
        if (data.user && !data.user.email_confirmed_at) {
            showMessage('signup-success', '🎉 Account created! Please check your email to confirm your account before signing in.', 'success');
            
            // Switch to login tab after a moment
            setTimeout(() => {
                switchTab('login');
            }, 3000);
        } else {
            showMessage('signup-success', '🎉 Account created successfully! Welcome to PortfolioBuilder!', 'success');
            
            // Wait a moment then hide modal and show authenticated state
            setTimeout(() => {
                hideModal();
                showAuthenticatedState(data.user);
            }, 2000);
        }
        
    } catch (error) {
        console.error('❌ Signup exception:', error);
        showMessage('signup-error', 'An unexpected error occurred. Please try again.');
    } finally {
        showLoading(false);
    }
}

async function handleLogout() {
    console.log('🚪 Logout initiated');
    
    if (!supabase) {
        console.error('❌ Supabase not available for logout');
        return;
    }
    
    showLoading(true);
    
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            console.error('❌ Logout error:', error);
            return;
        }
        
        console.log('🟢 Logout success');
        showUnauthenticatedState();
        
        // Smooth scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
    } catch (error) {
        console.error('❌ Logout exception:', error);
    } finally {
        showLoading(false);
    }
}

// Check Authentication State
async function checkAuthState() {
    console.log('🔍 Checking authentication state...');
    
    if (!supabase) {
        console.log('❌ Supabase not initialized, showing unauthenticated state');
        showUnauthenticatedState();
        return;
    }
    
    try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
            console.error('❌ Auth state check error:', error);
            showUnauthenticatedState();
            return;
        }
        
        if (session && session.user) {
            console.log('✅ User is authenticated:', session.user.email);
            showAuthenticatedState(session.user);
        } else {
            console.log('ℹ️ No active session');
            showUnauthenticatedState();
        }
        
    } catch (error) {
        console.error('❌ Auth state check exception:', error);
        showUnauthenticatedState();
    }
}

// Event Listeners
function setupEventListeners() {
    console.log('🎯 Setting up event listeners...');

    // Auth buttons
    document.getElementById('login-btn').addEventListener('click', () => {
        console.log('🔘 Login button clicked');
        switchTab('login');
        showModal();
    });

    document.getElementById('signup-btn').addEventListener('click', () => {
        console.log('🔘 Signup button clicked');
        switchTab('signup');
        showModal();
    });

    document.getElementById('get-started-btn').addEventListener('click', () => {
        console.log('🔘 Get started button clicked');
        switchTab('signup');
        showModal();
    });

    document.getElementById('logout-btn').addEventListener('click', handleLogout);

    // Modal close
    document.getElementById('modal-close').addEventListener('click', hideModal);

    // Modal overlay click
    authModal.addEventListener('click', (e) => {
        if (e.target === authModal) {
            hideModal();
        }
    });

    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            switchTab(btn.dataset.tab);
        });
    });

    // Form submissions
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    document.getElementById('signup-form').addEventListener('submit', handleSignup);

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && authModal.classList.contains('show')) {
            hideModal();
        }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    console.log('✅ Event listeners setup complete');
}

// Header scroll effect
function setupScrollEffects() {
    const header = document.querySelector('.header');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        }

        lastScrollY = currentScrollY;
    });
}

// Initialize App
async function initApp() {
    console.log('🚀 Initializing Professional Portfolio App...');

    // Initialize Supabase
    const supabaseInitialized = initSupabase();

    // Setup event listeners
    setupEventListeners();

    // Setup scroll effects
    setupScrollEffects();

    // Check initial auth state
    if (supabaseInitialized) {
        await checkAuthState();

        // Listen for auth state changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('🔄 Auth state changed:', event);

            if (event === 'SIGNED_IN' && session) {
                showAuthenticatedState(session.user);
            } else if (event === 'SIGNED_OUT') {
                showUnauthenticatedState();
            }
        });
    } else {
        showUnauthenticatedState();
    }

    console.log('✅ Professional Portfolio App initialized successfully');
}

// Start the app
document.addEventListener('DOMContentLoaded', initApp);

// Global test functions for debugging
window.testModal = function() {
    console.log('🧪 Testing modal...');
    switchTab('login');
    showModal();

    // Test input fields
    setTimeout(() => {
        const emailInput = document.getElementById('login-email');
        const passwordInput = document.getElementById('login-password');

        if (emailInput && passwordInput) {
            emailInput.value = '<EMAIL>';
            passwordInput.value = 'testpassword';
            emailInput.focus();
            console.log('✅ Input fields are working and accessible');
        } else {
            console.log('❌ Input fields not found');
        }
    }, 500);
};

window.testSupabase = function() {
    console.log('🧪 Testing Supabase connection...');
    console.log('Supabase object:', supabase);
    console.log('Configuration:', SUPABASE_CONFIG);

    if (supabase) {
        console.log('✅ Supabase is initialized');
    } else {
        console.log('❌ Supabase is not initialized');
    }
};

window.showDashboard = function() {
    console.log('🧪 Testing dashboard view...');
    const mockUser = { email: '<EMAIL>' };
    showAuthenticatedState(mockUser);
};
