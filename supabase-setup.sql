-- Portfolio Builder Database Setup
-- Run this SQL in your Supabase SQL Editor
-- Project: lwmqufwuzmmruecyaxgg

-- Create portfolios table
CREATE TABLE IF NOT EXISTS portfolios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    username TEXT UNIQUE,
    bio TEXT NOT NULL,
    skills TEXT[] NOT NULL DEFAULT '{}',
    github_url TEXT,
    linkedin_url TEXT,
    projects JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can insert their own portfolio" ON portfolios;
DROP POLICY IF EXISTS "Users can view their own portfolio" ON portfolios;
DROP POLICY IF EXISTS "Users can update their own portfolio" ON portfolios;
DROP POLICY IF EXISTS "Anyone can view portfolios for public sharing" ON portfolios;

-- Create RLS policies
CREATE POLICY "Users can insert their own portfolio" ON portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own portfolio" ON portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own portfolio" ON portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view portfolios for public sharing" ON portfolios
    FOR SELECT USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_portfolios_username ON portfolios(username);
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolios_created_at ON portfolios(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_portfolios_updated_at ON portfolios;
CREATE TRIGGER update_portfolios_updated_at
    BEFORE UPDATE ON portfolios
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data (optional - remove if you don't want sample data)
-- This creates a sample portfolio for testing
INSERT INTO portfolios (
    user_id,
    name,
    username,
    bio,
    skills,
    github_url,
    linkedin_url,
    projects
) VALUES (
    '00000000-0000-0000-0000-000000000000', -- This will fail but shows the structure
    'Sample User',
    'sample-user',
    'This is a sample portfolio to demonstrate the structure.',
    ARRAY['JavaScript', 'React', 'Node.js', 'PostgreSQL'],
    'https://github.com/sample-user',
    'https://linkedin.com/in/sample-user',
    '[
        {
            "title": "Portfolio Builder App",
            "description": "A full-stack web application for creating professional portfolios with authentication and database storage."
        },
        {
            "title": "E-commerce Website",
            "description": "A responsive e-commerce platform built with modern web technologies and payment integration."
        }
    ]'::jsonb
) ON CONFLICT DO NOTHING;

-- Verify the setup
SELECT 
    'Table created successfully' as status,
    COUNT(*) as row_count 
FROM portfolios;

-- Show table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'portfolios' 
ORDER BY ordinal_position;
